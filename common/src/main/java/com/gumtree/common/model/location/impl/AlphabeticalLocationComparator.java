package com.gumtree.common.model.location.impl;

import com.gumtree.domain.location.Location;
import org.springframework.util.Assert;

import java.util.Comparator;

/**
 * The default {@link java.util.Comparator} for comparing two location with each other.
 */
public class AlphabeticalLocationComparator implements Comparator<Location> {

    /**
     * {@inheritDoc}
     */
    @Override
    public final int compare(Location location1, Location location2) {
        Assert.notNull(location1);
        Assert.notNull(location2);

        return location1.getDisplayName().toUpperCase().compareTo(location2.getDisplayName().toUpperCase());
    }
}
