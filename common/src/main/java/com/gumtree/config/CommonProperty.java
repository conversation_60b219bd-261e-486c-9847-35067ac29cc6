package com.gumtree.config;

import com.gumtree.common.properties.GtProps;

public enum CommonProperty implements GtProps.GtProperty {


    SECURE_IMAGE_DOMAIN("gumtree.images.eps.httpshost", "SSL endpoint for EPS images"),
    RECAPTCHA_SITE_KEY("gumtree.recaptcha.siteKey", "Recaptcha site key"),
    RECAPTCHA_SECRET("gumtree.recaptcha.secret", "Recaptcha secret"),
    RECAPTCHA_MODE("gumtree.recaptcha.mode", "Recaptcha working mode. Supported values: disabled/normal/strict"),
    CVSTORE_HOST("gumtree.cv_store.host", "The CV Store host including protocol"),
    CVSTORE_PORT("gumtree.cv_store.port", "The CV Store port"),
    CVSTORE_VERIFY_SSL_CERTS("gumtree.cv_store.verify_ssl_certs", "The CV Store, should client verify ssl certificates?"),
    CVSTORE_ENABLED("gumtree.cv_store.enabled", "Is the CV Store client enabled"),
    CVSTORE_CONNECTION_TIMEOUT("gumtree.cv_store.connection_timeout", "Connection timeout for the CV Store client"),
    CVSTORE_SOCKET_TIMEOUT("gumtree.cv_store.socket_timeout", "Socket timeout for the CV Store client"),
    CVSTORE_STUB_ENABLED("gumtree.cv_store.stub.enabled", "Should I start the CV Store stub?"),
    EMAIL_ENCRYPTION_KEY("gumtree.email.encryption.key", "Key for emailAddress encryption"),

    // Madgex - Gumtree application API
    MADGEX_WEB_URL("gumtree.madgex.web.url", ""),
    MADGEX_WEB_POSTAD_URL("gumtree.madgex.web.postadurl", ""),

    JOBS_URL_PREFIX("gumtree.jobs.url.prefix", ""),
    BAPI_LOCATION_REFRESH_INTERVAL("gumtree.bapi.location.refresh.minutes", "How often to reload locations from BAPI"),

    // Home Bff API
    STATIC_ASSET_URL("gumtree.mobile.web.static.assets.url", "URL of static assets"),

    // k8s Ingress Authentication
    K8S_INGRESS_USER("gumtree.k8s.ingress.user", "k8s auth user"),
    K8S_INGRESS_PASSWORD("gumtree.k8s.ingress.password", "k8s auth password");

    private String propertyName;
    private String description;

    private CommonProperty(String propertyName, String description) {
        this.propertyName = propertyName;
        this.description = description;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public String getDescription() {
        return description;
    }
}
