package com.gumtree.util;

import com.google.common.base.Function;
import com.google.common.base.Optional;


public final class GuavaUtils {

    private GuavaUtils() {

    }

    /**
     * Implementation of flatMap operation on Guava Optional.
     * @param input Optional monad containing value to be transformed
     * @param f function to be performed on input's value
     * @param <F> input type
     * @param <T> return type
     * @return Optional monad with result of the transformation f on the input value, absent if the input value
     * was Optional.absent() or the transformation returned Optional.absent()
     */
    public static <F, T> Optional<T> flatMap(final Optional<F> input, final Function<F, Optional<T>> f) {
        return input.transform(f).or(Optional.<T>absent());
    }

    public static <T> Optional<T> java8OptionalToGuava(java.util.Optional<T> in) {
        return Optional.fromNullable(in.orElse(null));
    }

    public static <T> java.util.Optional<T> guavaOptionalTojava8(Optional<T> in) {
        return java.util.Optional.ofNullable(in.orNull());
    }

    /**
     * Shorter alias for guavaOptionalTojava8
     */
    public static <T> java.util.Optional<T> optionOf(Optional<T> in) {
        return guavaOptionalTojava8(in);
    }
}
