package com.gumtree.util.collection;

import org.junit.Test;

import java.util.Collections;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

public class PaginationListTest {

    @Test
    public void shouldConstructValidPaginationList() {
        // given
        List<String> list = Collections.singletonList("name");

        // when
        PaginationList<String> result = new PaginationList<String>(list, 1);

        // then
        assertThat(result.getResultsSize(), is(1));
        assertThat(result.getResults(), is(list));
        assertThat(result.isEmpty(), is(false));
    }
}
