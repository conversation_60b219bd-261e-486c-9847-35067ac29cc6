package com.gumtree.content.domain;

import org.apache.commons.lang.builder.HashCodeBuilder;

/**
 * A safety tip data object
 */
public class SafetyTip implements Comparable<SafetyTip> {

    private String message;
    private Long categoryId;
    private Integer priority;

    public SafetyTip() {

    }

    public SafetyTip(Long categoryId, Integer priority, String message) {
        this.categoryId = categoryId;
        this.priority = priority;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SafetyTip)) {
            return false;
        }
        SafetyTip safetyTip = (SafetyTip) o;
        if (safetyTip.categoryId != this.categoryId) {
            return false;
        }
        if (safetyTip.priority != this.priority) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(categoryId).append(priority).toHashCode();
    }

    @Override
    public int compareTo(SafetyTip safetyTip) {
        int comparePriority = priority.compareTo(safetyTip.getPriority());
        return (comparePriority != 0 ? comparePriority : categoryId.compareTo(safetyTip.getCategoryId()));
    }
}
