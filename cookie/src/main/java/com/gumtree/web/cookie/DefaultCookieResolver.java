package com.gumtree.web.cookie;

import com.gumtree.common.properties.GtProps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.context.request.NativeWebRequest;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class DefaultCookieResolver implements CookieResolver {

    private Map<Class, CookieCutter> cookieCuttersMap = new HashMap<>();

    @Autowired
    public void setCookieCutters(CookieCutter[] cookieCutters) {
        for(CookieCutter cutter: cookieCutters) {
            cookieCuttersMap.put(cutter.getSupportedCookieType(), cutter);
        }
    }

    @Override
    public <T extends BaseCookie> T resolve(NativeWebRequest webRequest, Class<T> cookieType) {
        HttpServletRequest request = (HttpServletRequest) webRequest.getNativeRequest();
        return resolve(request, cookieType);
    }

    @Override
    public <T extends BaseCookie> T resolve(HttpServletRequest request, Class<T> cookieType) {
        Assert.notNull(request);
        Assert.notNull(cookieType);

        CookieCutter cookieCutter = cookieCuttersMap.get(cookieType);
        if (cookieCutter != null) {
            return (T) cutCookie(request, cookieCutter);
        }

        throw new CookieCutterNotFoundException("no cookie cutter exists for cookie type " + cookieType);
    }

    private  <T extends BaseCookie> T cutCookie(HttpServletRequest request, CookieCutter<T> cookieCutter) {
        String cookieName = cookieCutter.getName(GtProps.getEnv());
        Object cachedCookie = request.getAttribute(cookieName);
        if (cachedCookie != null) {
            return (T) cachedCookie;
        } else {
            T cookie;

            Optional<Cookie> optionalCookie =
                    CookieUtils.findCookie(request.getCookies(), cookieName);
            if (optionalCookie.isPresent()) {
                cookie = cookieCutter.cutExisting(optionalCookie.get());
            } else {
                cookie = cookieCutter.cutNew();
            }

            request.setAttribute(cookieName, cookie);

            return cookie;
        }
    }
}
