package com.gumtree.web.reporting.threatmetrix;

import com.gumtree.web.common.thirdparty.ThirdPartyRequestContext;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.zeno.core.domain.PageType;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreatMetrixViewModelAppenderTest {
    @InjectMocks private ThreatMetrixViewModelAppender appender;
    @Mock private ThreatMetrix annotation;
    @Mock private ThirdPartyRequestContext context;
    @Mock private CookieResolver cookieResolver;
    @Mock private ThreatMetrixCookie cookie;


    private HashMap<String, Object> model = new HashMap<String, Object>();

    @Before
    public void before() {
        appender.setOrganisationId("orgId123");

        when(annotation.enabledKey()).thenReturn("tmSwitch");
    }

    @Test
    public void shouldBeDisabledByDefault() {
        // when
        appender.append(model, annotation, context);

        // then
        verifyZeroInteractions(context);
        assertThat(model.size(), is(0));
    }

    @Test
    public void shouldBeAllowToDisableItThroughModelParam() {
        // given
        appender.setEnabled(true);
        model.put("tmSwitch", false);

        // when
        appender.append(model, annotation, context);

        // then
        verifyZeroInteractions(context);
        assertThat(model.get(ThreatMetrixViewModelAppender.MODEL_PROP_NAME), nullValue());
    }

    @Test
    public void shouldAddThreatMetrixTrackingDataToModel() {
        // given
        appender.setEnabled(true);
        when(context.getPageType()).thenReturn(PageType.AccountCreate);

        when(cookieResolver.resolve(context.getHttpServletRequest(), ThreatMetrixCookie.class)).thenReturn(cookie);

        // when
        appender.append(model, annotation, context);

        // then should add tracking data to model
        ThreatMetrixTracking trackingData = (ThreatMetrixTracking) model.get(ThreatMetrixViewModelAppender.MODEL_PROP_NAME);
        assertThat(trackingData, notNullValue());
        assertThat(trackingData.getOrgId(), is("orgId123"));
        assertThat(trackingData.getPageType(), is("1"));

        // then should add session id cookie
        verify(cookie).getDefaultValue();
    }

    @Test
    public void shouldAddThreatMetrixTrackingDataToModelWhenUserRegistrationFormPageType() {
        // given
        appender.setEnabled(true);
        when(context.getPageType()).thenReturn(PageType.UserRegistrationForm);

        when(cookieResolver.resolve(context.getHttpServletRequest(), ThreatMetrixCookie.class)).thenReturn(cookie);

        // when
        appender.append(model, annotation, context);

        // then should add tracking data to model
        ThreatMetrixTracking trackingData = (ThreatMetrixTracking) model.get(ThreatMetrixViewModelAppender.MODEL_PROP_NAME);
        assertThat(trackingData, notNullValue());
        assertThat(trackingData.getOrgId(), is("orgId123"));
        assertThat(trackingData.getPageType(), is("1"));

        // then should add session id cookie
        verify(cookie).getDefaultValue();
    }


    @Test
    public void shouldAddThreatMetrixTrackingDataToModelWhenEditAd() {
        // given
        appender.setEnabled(true);
        when(context.getPageType()).thenReturn(PageType.EditAd);

        when(cookieResolver.resolve(context.getHttpServletRequest(), ThreatMetrixCookie.class)).thenReturn(cookie);

        // when
        appender.append(model, annotation, context);

        // then should add tracking data to model
        ThreatMetrixTracking trackingData = (ThreatMetrixTracking) model.get(ThreatMetrixViewModelAppender.MODEL_PROP_NAME);
        assertThat(trackingData, notNullValue());
        assertThat(trackingData.getOrgId(), is("orgId123"));
        assertThat(trackingData.getPageType(), is("1"));

        // then should add session id cookie
        verify(cookie).getDefaultValue();
    }

    @Test
    public void shouldAddThreatMetrixTrackingDataToModelWhenPostAd() {
        // given
        appender.setEnabled(true);
        when(context.getPageType()).thenReturn(PageType.PostAd);

        when(cookieResolver.resolve(context.getHttpServletRequest(), ThreatMetrixCookie.class)).thenReturn(cookie);

        // when
        appender.append(model, annotation, context);

        // then should add tracking data to model
        ThreatMetrixTracking trackingData = (ThreatMetrixTracking) model.get(ThreatMetrixViewModelAppender.MODEL_PROP_NAME);
        assertThat(trackingData, notNullValue());
        assertThat(trackingData.getOrgId(), is("orgId123"));
        assertThat(trackingData.getPageType(), is("1"));

        // then should add session id cookie
        verify(cookie).getDefaultValue();
    }
}
