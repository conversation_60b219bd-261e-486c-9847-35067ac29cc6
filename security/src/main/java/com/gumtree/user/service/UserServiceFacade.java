package com.gumtree.user.service;

import com.gumtree.seller.domain.deactivation.entity.DeactivationReason;
import com.gumtree.user.service.api.AccountDeactivationApi;
import com.gumtree.user.service.api.MarketingPreferenceApi;
import com.gumtree.user.service.api.UserInformationApi;
import com.gumtree.user.service.api.UserManagementApi;
import com.gumtree.user.service.model.*;
import com.gumtree.user.service.support.builder.UserApiErrorsBuilder;
import com.gumtree.user.service.support.exception.UserApiClientException;
import com.gumtree.user.service.support.exception.UserApiServerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import rx.functions.Func1;

/**
 * Simplify user service interactions.
 */
public class UserServiceFacade {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserServiceFacade.class);

    private UserManagementApi userManagementApi;
    private MarketingPreferenceApi marketingPreferenceApi;
    private AccountDeactivationApi accountDeactivationApi;
    private UserInformationApi userInformationApi;

    public UserServiceFacade(UserManagementApi userManagementApi, MarketingPreferenceApi marketingPreferenceApi,
                             AccountDeactivationApi accountDeactivationApi,UserInformationApi userInformationApi) {
        this.userManagementApi = userManagementApi;
        this.marketingPreferenceApi = marketingPreferenceApi;
        this.accountDeactivationApi = accountDeactivationApi;
        this.userInformationApi = userInformationApi;
    }

    public ApiResponse<Boolean> activate(UserActivationRequest userActivationRequest) {

        return userManagementApi.activate(userActivationRequest)
                .map(httpEntity -> ApiResponse.of(true))
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<AuthenticationResponse> authenticate(AuthenticationRequest authenticationRequest) {
        return userManagementApi.authenticate(authenticationRequest)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<GumtreeAccessToken> changePassword(ChangePasswordRequest changePasswordRequest) {
        return userManagementApi.changePassword(changePasswordRequest)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<UserTrustScoreResp> getUserTrustScore(UserTrustScoreReq userTrustScoreReq) {
        return userManagementApi.getUserTrustScore(userTrustScoreReq)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<UserResponse> getUserById(Long userId) {
        return userInformationApi.getUserById(userId)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<UserResponse> getUserByEmail(String email) {
        return userInformationApi.getUserByEmailAddress(email)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public void logout(LogoutRequest logoutRequest) {
        try {
            userManagementApi.logout(logoutRequest);
        } catch (Exception e) {
            LOGGER.error("Logout Exception: {}, {}", logoutRequest.getToken(), e.getMessage());
        }
    }

    public ApiResponse<RegisteredUser> registerUser(UserRegistrationRequest userRegistrationRequest) {
        return userManagementApi.registerUser(userRegistrationRequest)
                .map(ApiResponse::of)
                .onErrorReturn(toUserApiErrors())
                .toBlocking()
                .value();
    }

    public ApiResponse<Boolean> resetPassword(ResetPasswordRequest resetPasswordRequest) {
        try {
            userManagementApi.resetPassword(resetPasswordRequest);
            return ApiResponse.of(true);
        } catch (Exception e) {
           return handleException(e,"Reset password Exception: {}");
        }
    }

    public ApiResponse<Boolean> verifyAccessToken(VerifyAccessTokenRequest verifyAccessTokenRequest) {
        try {
            return userManagementApi.verifyAccessToken(verifyAccessTokenRequest)
                    .map(v -> ApiResponse.of(true))
                    .onErrorReturn(toUserApiErrors())
                    .toBlocking()
                    .value();
        } catch (Exception e){
            return handleException(e,"Verify Access Token Exception: {}");
        }
    }

    public ApiResponse<VerificationDetails> verifyCapiAccessToken(String cookie) {
        try {
            return userManagementApi.verifyCapiAccessToken(cookie)
                    .map(ApiResponse::of)
                    .onErrorReturn(toUserApiErrors())
                    .toBlocking()
                    .value();
        } catch (Exception e){
            return handleException(e,"Verify Capi Access Token Exception: {}");
        }
    }

    public ApiResponse<Boolean> setMarketingPreference(Long userId, boolean optInMarketing){
        try {
            return marketingPreferenceApi.setMarketingPreference(userId, optInMarketing)
                    .map(x -> ApiResponse.of(true))
                    .onErrorReturn(toUserApiErrors())
                    .toBlocking()
                    .value();
        } catch (Exception e){
            return handleException(e,"Exception {}");
        }
    }

    public ApiResponse<Boolean> initiateAccountDeactivation(Long userId, DeactivationReason reason){
        try {
            DeactivationTokenRequest request = new DeactivationTokenRequest();
            request.setReason(reason.getValue());
            return accountDeactivationApi.createDeactivationToken(userId, request)
                    .map(x -> ApiResponse.of(true))
                    .onErrorReturn(toUserApiErrors())
                    .toBlocking()
                    .value();
        } catch (Exception e){
            return handleException(e,"Exception {}");
        }
    }

    public ApiResponse<Boolean> finalizeAccountDeactivation(String deactivationToken){
        try {
            DeactivationUserRequest request = new DeactivationUserRequest();
            request.setToken(deactivationToken);
            accountDeactivationApi.postDeactivationToken(request);
            return ApiResponse.of(true);
        } catch (Exception e){
            LOGGER.error("Logout Exception: {}, {}", deactivationToken, e.getMessage());
            return handleException(e, "Finalize Account Deactivation Exception: {}");
        }
    }

    private <T> ApiResponse<T> handleException(Exception exception, String log) {
        if (exception instanceof UserApiClientException) {
            UserApiClientException cause = (UserApiClientException) exception;
            return ApiResponse.error(getUserApiClientErrors(cause));
        } else if (exception instanceof UserApiServerException) {
            UserApiServerException cause = (UserApiServerException) exception;
            return ApiResponse.error(getUserApiServerErrors(cause));
        } else {
            LOGGER.warn(log, exception.getMessage());
            return ApiResponse.error(unexpectedError());
        }
    }

    private <T> Func1<Throwable, ApiResponse<T>> toUserApiErrors() {
        return throwable -> {
            if(throwable instanceof UserApiClientException) {
                UserApiClientException cause = (UserApiClientException) throwable;
                return ApiResponse.error(getUserApiClientErrors(cause));
            } else if(throwable instanceof UserApiServerException) {
                UserApiServerException cause = (UserApiServerException) throwable;
                return ApiResponse.error(getUserApiServerErrors(cause));
            }
            else {
                LOGGER.error("User Service call failed", throwable);
                return ApiResponse.error(unexpectedError());
            }
        };
    }

    private UserApiErrors getUserApiClientErrors(UserApiClientException cause) {
        return cause.getUserApiErrors()
                .orElseGet(() -> {
                    LOGGER.info("No API Client Errors Set {}", cause.getMessage());
                    return UserApiErrorsBuilder.builder()
                            .withErrorCode(cause.getMessage())
                            .build();
                });
    }

    private UserApiErrors getUserApiServerErrors(UserApiServerException cause) {
        return cause.getUserApiErrors()
                .orElseGet(() -> {
                    LOGGER.info("No API Server Errors Set {}", cause.getMessage());
                    return UserApiErrorsBuilder.builder()
                            .withErrorCode(cause.getMessage())
                            .build();
                });
    }

    private UserApiErrors unexpectedError() {
        return UserApiErrorsBuilder.builder()
                .withErrorCode(UserApiErrorCode.UNEXPECTED_ERROR)
                .build();
    }

    public static UserApiErrorCode safeValueOf(String value) {
        try {
            return UserApiErrorCode.valueOf(value);
        } catch (Exception ex) {
            return UserApiErrorCode.UNKNOWN_API_ERROR;
        }
    }
}
