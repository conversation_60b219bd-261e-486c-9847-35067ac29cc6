package com.gumtree.web.cookie;

import com.gumtree.common.properties.Env;
import com.gumtree.web.cookie.cutters.messagecentre.MessageCentreCookieCutter;
import com.netflix.config.ConfigurationManager;
import org.junit.Before;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Properties;

import static org.fest.assertions.api.Assertions.assertThat;

public class MessageCentreCookieHelperTest {

    static {
        Properties properties = new Properties();
        properties.setProperty("gumtree.env", Env.PROD.name());
        properties.setProperty("gumtree.cookies.secure", "false");
        ConfigurationManager.loadProperties(properties);
    }

    private static final String COOKIE_HEADER_NAME = "Set-Cookie";

    private MessageCentreCookieHelper messageCentreCookieHelper;

    @Before
    public void setup() {
        DefaultCookieResolver cookieResolver = new DefaultCookieResolver();
        MessageCentreCookieCutter messageCentreCookieCutter = new MessageCentreCookieCutter("testDomain");
        cookieResolver.setCookieCutters(new CookieCutter[] {messageCentreCookieCutter});
        messageCentreCookieHelper =
                new MessageCentreCookieHelper(messageCentreCookieCutter, cookieResolver);
    }

    @Test
    public void itAddsTheRightHeaderWithCookieToSet() throws Exception {
        ServletRequest request = aRequest();
        ServletResponse response = aResponse();

        messageCentreCookieHelper.addMessageCentreCookieForPrincipal(request, response);

        assertThat(((HttpServletResponse)response).containsHeader(COOKIE_HEADER_NAME)).isTrue();
        assertThat(((HttpServletResponse)response).getHeader(COOKIE_HEADER_NAME)).containsIgnoringCase("gt_mc=");
        assertThat(((HttpServletResponse)response).getHeader(COOKIE_HEADER_NAME)).containsIgnoringCase("nuc");
        assertThat(((HttpServletResponse)response).getHeader(COOKIE_HEADER_NAME)).containsIgnoringCase("rcd");
        assertThat(((HttpServletResponse)response).getHeader(COOKIE_HEADER_NAME)).containsIgnoringCase("Path=/");
        assertThat(((HttpServletResponse)response).getHeader(COOKIE_HEADER_NAME)).containsIgnoringCase("Domain=testDomain");
    }

    @Test
    public void itAdsTheRightHeaderToRemoveCookieFromResponse() throws Exception {
        ServletRequest request = aRequest();
        ServletResponse response = aResponse();

        messageCentreCookieHelper.removeMessageCentreCookie((HttpServletRequest)request, (HttpServletResponse)response);

        assertThat(((MockHttpServletResponse)response).getCookie("gt_mc")).isNotNull();
        assertThat(((MockHttpServletResponse)response).getCookie("gt_mc").getMaxAge()).isEqualTo(0);
    }

    private ServletResponse aResponse() {
        return new MockHttpServletResponse();
    }

    private ServletRequest aRequest() {
        return new MockHttpServletRequest() {
        };
    }
}