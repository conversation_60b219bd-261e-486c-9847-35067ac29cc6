package com.gumtree.web.security.testing;


import com.google.common.base.Optional;
import com.gumtree.api.SocialData;
import com.gumtree.api.User;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.Before;
import org.junit.Test;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SocialLoginUtilTest {

    private HttpServletRequest httpServletRequest;

    @Before
    public void init() {
        httpServletRequest = mock(HttpServletRequest.class);
    }

    @Test
    public void shouldBeFalseIfNoUser() {
        boolean result = SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                Optional.<User>absent(), false);
        assertFalse("No User, so no marketing option",result);
    }

    @Test
    public void shouldBeFalseIfUserNotActivated() {

        final boolean createdToday = true;
        final boolean activated = false;
        final boolean optedInMarketing = false;

        final User user = new UserBuilder(createdToday, activated, optedInMarketing).withSocialData().build();
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), false);
        assertFalse("User is not activated, so no marketing option",result);
    }

    @Test
    public void shouldBeFalseIfOptIn() {

        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = true;

        final User user = new UserBuilder(createdToday, activated, optedInMarketing).withSocialData().build();
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), false);
        assertFalse("User is opted in to marketing already, so no marketing option", result);
    }

    @Test
    public void shouldBeFalseIfAlreadyExisting() {
        final DateTime today = new DateTime().minusSeconds(1);
        User user = new User();
        user.setOptInMarketing(false);
        user.setCreationDate(today);
        user.setActivationDate(today.plusDays(12));
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), false);
        assertFalse(result);
    }

    @Test
    public void shouldBeFalseIfSellerNotFromLogin() {
        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;
        final User user = new UserBuilder(createdToday, activated, optedInMarketing).withSocialData().build();
        when(httpServletRequest.getHeader("referer")).thenReturn("/registration");
        final boolean isSeller = true;
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), isSeller);
        assertFalse(result);
    }


    @Test
    public void shouldBeFalseIfNotSocialLogin() {
        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;
        final User user = new UserBuilder(createdToday, activated, optedInMarketing).build();
        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), false);
        assertFalse(result);
    }

    @Test
    public void shouldBeTrueWithAllCondition() {
        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;
        final User user = new UserBuilder(createdToday, activated, optedInMarketing).withSocialData().build();
        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertTrue(result);
    }

    @Test
    public void shouldBeTrueWithActivationWithInLastMinute() {

        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;

        // within the last minute test
        DateTime activationDateTime = new DateTime(DateTimeZone.UTC).minusSeconds(50);
        User user = new UserBuilder(createdToday, activated, optedInMarketing)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime)
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertTrue(result);

        // activated more than 1 minute previously
        activationDateTime = new DateTime(DateTimeZone.UTC).minusSeconds(63);
        user = new UserBuilder(createdToday, activated, optedInMarketing)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime)
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertFalse(result);


        // activated more than 1 minute previously
        activationDateTime = new DateTime(DateTimeZone.UTC).plusMinutes(2);
        user = new UserBuilder(createdToday, activated, optedInMarketing)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime)
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertFalse(result);
    }

    @Test
    public void shouldBeFalseIfActivationDateAndCreateDateAreDifferent() {

        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;

        // make create date before activation date
        DateTime activationDateTime = new DateTime().minusSeconds(30);
        User user = new UserBuilder(createdToday, activated, optedInMarketing)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime.minusSeconds(10))
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn("/login");
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertFalse(result);
    }

    @Test
    public void shouldBeTrueIfReferredFromCreateAccount() {
        // within the last minute test
        DateTime activationDateTime = new DateTime(DateTimeZone.UTC).minusSeconds(50);
        User user = new UserBuilder(true, true, false)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime)
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn("/create-account");
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), true);
        assertTrue(result);
    }

    @Test
    public void shouldNotThrowExceptionIfComingFromBuyer() {

        final boolean createdToday = true;
        final boolean activated = true;
        final boolean optedInMarketing = false;

        // make create date before activation date
        DateTime activationDateTime = new DateTime().minusSeconds(50);
        User user = new UserBuilder(createdToday, activated, optedInMarketing)
                .withSocialData()
                .withActivationDateTime(activationDateTime)
                .withCreatedDateTime(activationDateTime)
                .build();

        when(httpServletRequest.getHeader("referer")).thenReturn(null);
        boolean result =
                SocialLoginUtil.isRequiringMarketingOptIn(httpServletRequest,
                        Optional.of(user), false);
        assertTrue(result);
    }


    static class UserBuilder {

        private User user;

        /**
         * Creates a user, if activated, then created date and activation date will be the same.
         *
         * @param createdToday     user created today
         * @param activated        user activated
         * @param optedInMarketing
         */
        UserBuilder(boolean createdToday, boolean activated, boolean optedInMarketing) {

            final User.Builder builder = User.builder();
            builder.withOptInMarketing(optedInMarketing);
            user = builder.build();

            final DateTime oneSecondAgo = new DateTime().minusSeconds(1);
            if (createdToday) {
                user.setCreationDate(oneSecondAgo);
            } else {
                user.setCreationDate(oneSecondAgo.minusDays(1));
            }
            if (activated) {
                // social login seems to have created == activation date
                user.setActivationDate(user.getCreationDate());
            }
        }

        UserBuilder withSocialData() {
            SocialData socialData = new SocialData();
            socialData.setPlatform("FB");
            socialData.setSocialId("123FB321");
            user.setSocialData(Arrays.asList(socialData));
            return this;
        }

        UserBuilder withActivationDateTime(DateTime activationDateTime) {
            user.setActivationDate(activationDateTime);
            return this;
        }

        UserBuilder withCreatedDateTime(DateTime createdDateTime) {
            user.setCreationDate(createdDateTime);
            return this;
        }

        User build() {
            return this.user;
        }
    }

}
