<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <name>Seller Server</name>
    <groupId>com.gumtree.web.seller</groupId>
    <artifactId>server</artifactId>
    <packaging>war</packaging>
    <description>Gumtree Seller web</description>

    <parent>
        <groupId>com.gumtree.web</groupId>
        <artifactId>seller</artifactId>
        <version>3.0-SNAPSHOT</version>
    </parent>

    <properties>
        <motors-api-contracts.version>1.0.8</motors-api-contracts.version>
        <payment-api-contracts.version>2.23.3a4f484</payment-api-contracts.version>
        <jersey.version>1.18</jersey.version>
        <swagger-annotations.version>1.5.9</swagger-annotations.version>
        <migbase64.version>2.2</migbase64.version>
        <pact-jvm-consumer-junit_2.11.version>3.3.7</pact-jvm-consumer-junit_2.11.version>
        <checkstyle.maxviolations>67</checkstyle.maxviolations>
        <swagger-codegen-maven-plugin.version>2.1.6</swagger-codegen-maven-plugin.version>
        <user-api-contracts.version>0.0.5</user-api-contracts.version>
        <account-api-contracts.version>0.0.6</account-api-contracts.version>
        <gt.bapi-contract.version>5.21.de64e12</gt.bapi-contract.version>
        <gt.media-processor.contract.version>2.5.f0741e8</gt.media-processor.contract.version>
        <gt.livead-search-contract.version>1.********.4</gt.livead-search-contract.version>
        <gt.fullad-search-contract.version>1.********.2</gt.fullad-search-contract.version>
        <gt.adcounters.contract.version>2.12.3088b51</gt.adcounters.contract.version>
        <authorisation.contract.version>2.1.9168ebe</authorisation.contract.version>
        <gt.phone.number-authenticator.contract.version>2025-05-08-12-47-21.master.9bd69f2</gt.phone.number-authenticator.contract.version>
        <gt.adsearch-category-predictor-contract.version>2025-06-20-03-42-35.master.90e7d97</gt.adsearch-category-predictor-contract.version>

    </properties>

    <dependencies>
        <dependency>
            <groupId>com.gumtree.security.phone-number-authenticator</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.phone.number-authenticator.contract.version}</version>
            <type>yaml</type>
        </dependency>
        <!-- JETTY dependencies -->
        <dependency>
            <groupId>com.gumtree.shared-commons.jetty</groupId>
            <artifactId>jetty-server-legacy</artifactId>
        </dependency>

        <!-- LOCAL dependencies -->
        <dependency>
            <groupId>com.gumtree.motors.motors-price-guidance</groupId>
            <artifactId>contract</artifactId>
            <type>yaml</type>
            <version>1.9.4f8c3f7</version>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>motors-api-contracts</artifactId>
            <version>${motors-api-contracts.version}</version>
            <classifier>vehicle-data</classifier>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.payment.payment-api</groupId>
            <artifactId>payment-api-contracts</artifactId>
            <version>${payment-api-contracts.version}</version>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>user-reviews-api-contracts</artifactId>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>user-reviews-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>job-applications-contracts-client-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>motors-syi-api-client</artifactId>
            <version>1.20181102.1</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>test-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>locations-contract</artifactId>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>livead-search-contract</artifactId>
            <version>${gt.livead-search-contract.version}</version>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.contracts</groupId>
            <artifactId>fullad-search-service-contract</artifactId>
            <version>${gt.fullad-search-contract.version}</version>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.adsearch.category-predictor</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.adsearch-category-predictor-contract.version}</version>
            <type>yaml</type>
            <exclusions>
                <exclusion>
                    <groupId>com.gumtree.shared-common.shared-api-base-contract</groupId>
                    <artifactId>base-contract</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- GUMTREE dependencies -->
        <dependency>
            <groupId>com.gumtree.drafts.api</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api.category</groupId>
            <artifactId>read-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.metrics</groupId>
            <artifactId>metrics-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>gumtree-common</groupId>
            <artifactId>test-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>gumtree-common</groupId>
            <artifactId>util</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>security</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>web-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>gumtree-common</groupId>
                    <artifactId>reports</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>web-common</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gumtree.shared-commons.properties</groupId>
            <artifactId>gtprops-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>reporting</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>content</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>google-analytics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.japi</groupId>
            <artifactId>model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.gumtree.api.category</groupId>
                    <artifactId>read-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gumtree.japi</groupId>
            <artifactId>client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gumtree</groupId>
            <artifactId>gas_2.10</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.joda</groupId>
                    <artifactId>joda-convert</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe</groupId>
                    <artifactId>config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.paranamer</groupId>
                    <artifactId>paranamer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.json4s</groupId>
                    <artifactId>json4s-jackson_2.10</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- MAVEN-PARENT dependencies -->
        <!-- scala -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang.modules</groupId>
            <artifactId>scala-xml_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20160810</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.1.2.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.ehcache-spring-annotations</groupId>
            <artifactId>ehcache-spring-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ebay.ecg</groupId>
            <artifactId>eps-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tiles</groupId>
            <artifactId>tiles-extras</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tiles</groupId>
            <artifactId>tiles-jsp</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.jcsv</groupId>
            <artifactId>jcsv</artifactId>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.zeno</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.zeno</groupId>
            <artifactId>domain</artifactId>
        </dependency>
        <dependency>
            <groupId>org.tuckey</groupId>
            <artifactId>urlrewritefilter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>ur6lad</groupId>
            <artifactId>markdown-taglib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.pegdown</groupId>
            <artifactId>pegdown</artifactId>
        </dependency>
        <dependency>
            <groupId>com.braintreepayments.gateway</groupId>
            <artifactId>braintree-java</artifactId>
            <version>2.72.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.extensions</groupId>
            <artifactId>spring-security-saml2-core</artifactId>
            <version>1.0.0.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.uadetector</groupId>
            <artifactId>uadetector-core</artifactId>
            <version>0.9.22</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sf.uadetector</groupId>
            <artifactId>uadetector-resources</artifactId>
            <version>2014.10</version>
            <exclusions>
                <exclusion>
                    <groupId>net.sf.uadetector</groupId>
                    <artifactId>uadetector-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.8.3</version>
        </dependency>

        <!-- PROMETHEUS dependencies-->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-jvm</artifactId>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_dropwizard</artifactId>
        </dependency>

        <!-- TEST dependencies -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gumtree.motors</groupId>
            <artifactId>test-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit</artifactId>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.easytesting</groupId>
            <artifactId>fest-assert-core</artifactId>
        </dependency>
        <dependency>
            <groupId>uk.co.datumedge</groupId>
            <artifactId>hamcrest-json</artifactId>
            <version>0.1</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>au.com.dius</groupId>
            <artifactId>pact-jvm-consumer-junit_2.11</artifactId>
            <version>${pact-jvm-consumer-junit_2.11.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>io.javaslang</groupId>
            <artifactId>javaslang</artifactId>
            <version>2.0.2</version>
        </dependency>
        <!-- HTTP client: jersey-client -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-core</artifactId>
            <version>${jersey.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>${jersey.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey.contribs</groupId>
            <artifactId>jersey-multipart</artifactId>
            <version>${jersey.version}</version>
        </dependency>

        <!-- JSON processing: jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <!-- Base64 encoding that works in both JVM and Android -->
        <dependency>
            <groupId>com.brsanthu</groupId>
            <artifactId>migbase64</artifactId>
            <version>${migbase64.version}</version>
        </dependency>

        <dependency>
            <groupId>com.gumtree.api</groupId>
            <artifactId>user-api-contract</artifactId>
            <version>${user-api-contracts.version}</version>
            <type>contract</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.api</groupId>
            <artifactId>account-api-contract</artifactId>
            <version>${account-api-contracts.version}</version>
            <type>contract</type>
        </dependency>
        <!-- HTTP client: Netflix Feign -->
        <!-- openfeign -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
        </dependency>

        <!-- remove asap, reqd for tests, need to refactor -->
        <dependency>
            <groupId>com.gumtree.user</groupId>
            <artifactId>user-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gumtree</groupId>
            <artifactId>shared-feign-hystrix-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gumtree.bapi</groupId>
            <artifactId>bapi-contract</artifactId>
            <version>${gt.bapi-contract.version}</version>
            <type>yaml</type>
        </dependency>
        <dependency>
            <groupId>com.gumtree.mediaprocessor.media-processor</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.media-processor.contract.version}</version>
            <type>yaml</type>
        </dependency>

        <dependency>
            <groupId>com.gumtree.adcounter.ad-counters</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.adcounters.contract.version}</version>
            <type>yaml</type>
            <exclusions>
                <exclusion>
                    <groupId>com.gumtree.shared-common.shared-api-base-contract</groupId>
                    <artifactId>base-contract</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- JWT Tokens -->
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>9.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.gumtree.shared-common.shared-api-base-contract</groupId>
            <artifactId>base-contract</artifactId>
            <version>2.3.9f3eb10</version>
            <type>yaml</type>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>spring-cloud-gcp-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${deb.package}</finalName>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.12</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/src/main/java</source>
                                <source>${project.build.directory}/generated-sources/src/main/resources</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-shared-resources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.gumtree</includeGroupIds>
                            <includeArtifactIds>shared-feign-hystrix-api-client</includeArtifactIds>
                            <outputDirectory>${project.build.directory}/shared-resources</outputDirectory>
                            <includes>**/*.mustache</includes>
                        </configuration>
                    </execution>
                    <!-- Copy contract for client generation -->
                    <execution>
                        <id>copy</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeTypes>contract</includeTypes>
                            <outputDirectory>${project.build.directory}/contracts</outputDirectory>
                            <stripVersion>true</stripVersion>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-contract</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeTypes>yaml</includeTypes>
                            <outputDirectory>${project.build.directory}/contracts</outputDirectory>
                            <stripVersion>true</stripVersion>
                            <prependGroupId>true</prependGroupId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- Generate client -->
            <plugin>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-codegen-maven-plugin</artifactId>
                <version>${swagger-codegen-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>motors-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/motors-api-contracts-vehicle-data.contract</inputSpec>
                            <language>java</language>
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.motors.vehicledata</apiPackage>
                            <invokerPackage>com.gumtree.seller.infrastructure.driven.motors.vehicledata</invokerPackage>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.motors.vehicledata.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>user-api-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/user-api-contract.contract</inputSpec>
                            <language>java</language>
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.user</apiPackage>
                            <invokerPackage>com.gumtree.seller.infrastructure.driven.user</invokerPackage>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.user.model</modelPackage>
                            <library>feign</library>
                            <templateDirectory>src/main/resources/Java</templateDirectory>
                            <configOptions>
                                <sourceFolder>src/main/java</sourceFolder>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>account-api-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/account-api-contract.contract</inputSpec>
                            <language>java</language>
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.account</apiPackage>
                            <invokerPackage>com.gumtree.seller.infrastructure.driven.account</invokerPackage>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.account.model</modelPackage>
                            <library>feign</library>
                            <templateDirectory>src/main/resources/Java</templateDirectory>
                            <configOptions>
                                <sourceFolder>src/main/java</sourceFolder>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>4.3.1</version>
                <executions>
                    <execution>
                        <id>locations-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/locations-contract.contract</inputSpec>
                            <templateDirectory>${project.basedir}/src/main/resources/Java/libraries/feign</templateDirectory>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <library>feign</library>
                                <serializableModel>false</serializableModel>
                            </configOptions>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.locations.api</apiPackage>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.model</modelPackage>
                            <invokerPackage>com.gumtree.seller.infrastructure.driven.locations.api</invokerPackage>
                            <supportingFilesToGenerate>ApiClient.java,HttpBasicAuth.java</supportingFilesToGenerate>
                            <modelNameSuffix>Model</modelNameSuffix>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <httpUserAgent>locations-generated-client</httpUserAgent>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <library>feign</library>
                            <httpUserAgent>seller</httpUserAgent>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-motors-price-guidance</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.motors.motors-price-guidance.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.motors.price.guidance.model</modelPackage>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.motors.price.guidance.api</apiPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <supportingFilesToGenerate>EncodingUtils.java,StringUtil.java</supportingFilesToGenerate>
                            <templateDirectory>src/main/resources/Java</templateDirectory>
                            <configOptions>
                                <library>feign</library>
                                <sourceFolder>src/main/java</sourceFolder>
                                <serializableModel>true</serializableModel>
                                <java8>true</java8>
                                <interfaceOnly>true</interfaceOnly>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <httpUserAgent>seller</httpUserAgent>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-bapi-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.bapi.bapi-contract.yaml</inputSpec>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <language>java</language>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.bapi</apiPackage>
                            <invokerPackage>com.gumtree.backend.infrastructure.bapi</invokerPackage>
                            <modelPackage>com.gumtree.bapi.model</modelPackage>
                            <library>feign</library>
                            <configOptions>
                                <dateLibrary>legacy</dateLibrary>
                            </configOptions>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <httpUserAgent>seller</httpUserAgent>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-media-processor-api</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.mediaprocessor.media-processor.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <library>feign</library>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <modelPackage>com.gumtree.mediaprocessor.model</modelPackage>
                            <apiPackage>com.gumtree.mediaprocessor.api</apiPackage>
                            <invokerPackage>com.gumtree.backend.infrastructure.mediaprocessor</invokerPackage>
                            <output>${project.build.directory}/generated-sources</output>
                            <generateModels>true</generateModels>
                            <generateApis>true</generateApis>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <httpUserAgent>seller</httpUserAgent>
                            <importMappings>
                                <importMapping>File=org.springframework.web.multipart.MultipartFile</importMapping>
                            </importMappings>
                            <typeMappings>
                                <typeMapping>File=MultipartFile</typeMapping>
                            </typeMappings>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-gumtree-adcounters-files</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.adcounter.ad-counters.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java17>true</java17>
                                <dateLibrary>java17</dateLibrary>
                                <library>feign</library>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <modelPackage>com.gumtree.adcounters.model</modelPackage>
                            <apiPackage>com.gumtree.adcounters</apiPackage>
                            <invokerPackage>com.gumtree.adcounters</invokerPackage>
                            <output>${project.build.directory}/generated-sources</output>
                            <generateModels>true</generateModels>
                            <generateApis>true</generateApis>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <httpUserAgent>seller</httpUserAgent>
                        </configuration>
                    </execution>

                    <execution>
                        <id>generate-fullad-search-service</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/fullad-search-service-contract.contract</inputSpec>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <generatorName>java</generatorName>
                            <library>feign</library>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <modelPropertyNaming>snake_case</modelPropertyNaming>
                            </configOptions>
                            <supportingFilesToGenerate>EncodingUtils.java,ParamExpander.java,RFC3339DateFormat.java,OAuth.java,StringUtil.java,ApiClient.java,ApiKeyAuth.java,HttpBearerAuth.java,HttpBasicAuth.java,OAuthFlow.java</supportingFilesToGenerate>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.fulladsearch</apiPackage>
                            <modelPackage>com.gumtree.fulladsearch.model</modelPackage>
                            <modelNamePrefix>FullAd</modelNamePrefix>
                            <invokerPackage>com.gumtree.fulladsearch</invokerPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-livead-search-client</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/livead-search-contract.contract</inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <library>feign</library>
                                <serializableModel>false</serializableModel>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <modelPackage>com.gumtree.liveadsearch.model</modelPackage>
                            <apiPackage>com.gumtree.liveadsearch.client</apiPackage>
                            <invokerPackage>com.gumtree.liveadsearch.client</invokerPackage>
                            <output>${project.build.directory}/generated-sources</output>
                            <generateModels>true</generateModels>
                            <generateApis>true</generateApis>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-category-predictor-client</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/contracts/com.gumtree.adsearch.category-predictor.contract.yaml
                            </inputSpec>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <library>feign</library>
                                <serializableModel>false</serializableModel>
                            </configOptions>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign
                            </templateDirectory>
                            <modelPackage>com.gumtree.category.predictor.model</modelPackage>
                            <apiPackage>com.gumtree.category.predictor.client</apiPackage>
                            <invokerPackage>com.gumtree.category.predictor.client</invokerPackage>
                            <output>${project.build.directory}/generated-sources</output>
                            <generateModels>true</generateModels>
                            <generateApis>true</generateApis>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                        </configuration>
                    </execution>
                    <execution>
                        <id>Phone Number Authenticator api </id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.build.directory}/contracts/com.gumtree.security.phone-number-authenticator.contract.yaml
                            </inputSpec>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <library>feign</library>
                                <sourceFolder>src/main/java</sourceFolder>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <output>${project.build.directory}/generated-sources</output>
                            <modelPackage>com.gumtree.security.phone.number.authenticator.model</modelPackage>
                            <apiPackage>com.gumtree.security.phone.number.authenticator.api</apiPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                        </configuration>
                    </execution>
                    <execution>
                        <id>payment-api-braintree</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.payment.payment-api.payment-api-contracts.yaml</inputSpec>
                            <templateDirectory>${project.build.directory}/shared-resources/Java.libraries.feign</templateDirectory>
                            <generatorName>java</generatorName>
                            <configOptions>
                                <library>feign</library>
                                <sourceFolder>src/main/java</sourceFolder>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <output>${project.build.directory}/generated-sources</output>
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                            <output>${project.build.directory}/generated-sources</output>
                            <apiPackage>com.gumtree.seller.infrastructure.driven.payment.braintree</apiPackage>
                            <modelPackage>com.gumtree.seller.infrastructure.driven.payment.braintree.model</modelPackage>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jdeb</id>
            <activation>
                <property>
                    <name>jdeb</name>
                    <value>enabled</value>
                </property>
            </activation>
            <build>
        	<finalName>${deb.package}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-war</id>
                                <phase>none</phase>
                            </execution>
                            <execution>
                                <id>package-jetty-war</id>
                                <phase>package</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <attachClasses>true</attachClasses>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>deb-maven-plugin</artifactId>
                        <groupId>com.gumtree.build.maven</groupId>
                        <version>5.130</version>
                        <configuration>
                            <projectType>jetty_server</projectType>
                            <serviceName>seller-server</serviceName>
                            <replaces>gumtree-seller-public-server-release,gumtree-seller-public-server</replaces>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>deb-package</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>acceptance-enabled</id>
            <activation>
                <property>
                    <name>gumtree.test.acceptance</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>tomcat-maven-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <configuration>
                                    <fork>true</fork>
                                    <useSeparateTomcatClassLoader>true</useSeparateTomcatClassLoader>
                                    <port>8484</port>
                                    <path>/</path>
                                    <warDirectory>${project.build.directory}/${project.build.finalName}</warDirectory>
                                    <systemProperties>
                                        <gumtree.api.host>http://localhost:8282/api</gumtree.api.host>
                                        <spring.profiles.active>stub-models</spring.profiles.active>
                                        <gumtree.legacy.manageads.login_url>http://localhost:9393/account/login
                                        </gumtree.legacy.manageads.login_url>
                                        <gumtree.cookies.default_domain>localhost</gumtree.cookies.default_domain>
                                    </systemProperties>
                                </configuration>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>run-war</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
