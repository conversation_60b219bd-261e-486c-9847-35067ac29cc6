package com.gumtree.web.seller.converter;

import com.google.common.base.Optional;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.Attribute;
import com.gumtree.api.LegacyImage;
import com.gumtree.api.category.domain.AttributeMetadata;
import com.gumtree.api.category.domain.AttributeType;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.category.domain.CategoryConstants;
import com.gumtree.api.domain.advert.DeleteReason;
import com.gumtree.common.format.PriceFormatter;
import com.gumtree.common.util.StringUtils;
import com.gumtree.domain.category.Categories;
import com.gumtree.domain.location.Location;
import com.gumtree.domain.newattribute.AttributeService;
import com.gumtree.domain.newattribute.AttributeValue;
import com.gumtree.domain.newattribute.DisplayAttribute;
import com.gumtree.domain.newattribute.internal.DefaultAttribute;
import com.gumtree.domain.newattribute.internal.InvalidAttributeValueException;
import com.gumtree.domain.newattribute.internal.value.DateValue;
import com.gumtree.location.LocationDisplayUtils;
import com.gumtree.seller.domain.image.entity.ImageSize;
import com.gumtree.service.category.CategoryService;
import com.gumtree.service.location.LocationService;
import com.gumtree.util.url.CdnImageUrlProvider;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.model.AdPreview;
import com.gumtree.web.seller.model.AdPreviewImpl;
import com.gumtree.web.common.format.PostingTimeFormatter;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Arrays;


/**
 * Default implementation of {@link AdToAdPreviewConverter}.
 */
@Component
public class DefaultAdToAdPreviewConverter implements AdToAdPreviewConverter {

    @Autowired
    private UrlScheme urlScheme;

    @Autowired
    private LocationService locationService;

    @Autowired
    private AttributeService attributeService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private PriceFormatter priceFormatter;

    @Autowired
    private CdnImageUrlProvider cdnImageUrlProvider;

    private DateTimeFormatter dateTimeFormatter = ISODateTimeFormat.dateTimeNoMillis();

    @Override
    public final AdPreview convert(Ad ad) {
        Long l1CategoryId = CategoryConstants.ALL_ID;
        Long l2CategoryId = null;
        Long l3CategoryId = null;
        Optional<Category> category = Optional.absent();
        if (ad.getCategoryId() != null) {
            final Long categoryId = ad.getCategoryId();
            category = categoryService.getById(categoryId);
            Map<Integer, Category> levelHierarchy = categoryService.getLevelHierarchy(categoryId);

            if (levelHierarchy.containsKey(1)) {
                l1CategoryId = levelHierarchy.get(1).getId();
            }

            if (levelHierarchy.containsKey(2)) {
                l2CategoryId = levelHierarchy.get(2).getId();
            }

            if (levelHierarchy.containsKey(3)) {
                l3CategoryId = levelHierarchy.get(3).getId();
            }
        }
        String displayLocationText = getDisplayLocationText(ad);
        Boolean urgent = isUrgent(ad);
        Boolean featured = isFeatured(ad);
        String contactEmail = getContactEmail(ad);

        AdPreviewImpl.Builder builder = new AdPreviewImpl.Builder();
        builder.id(ad.getId())
                .status(ad.getStatus())
                .title(ad.getTitle())
                .description(ad.getDescription())
                .isUrgent(urgent)
                .isFeatured(featured)
                .advertUrl(urlScheme.urlFor(ad))
                .reportAdUrl(urlScheme.urlToReportAd(ad.getId()))
                .editAdvertUrl(urlScheme.editUrlFor(ad))
                .restoreAdvertUrl(urlScheme.restoreUrlFor(ad))
                .displayLocationText(displayLocationText)
                .contactEmail(contactEmail)
                .displayPrice(priceFormatter.format(
                        ad.getPrice(),
                        ad.getPriceFrequency()))
                .isCategoryReadOnly(isCategoryReadOnly(category))
                .l1CategoryId(l1CategoryId)
                .l2CategoryId(l2CategoryId)
                .l3CategoryId(l3CategoryId)
                .markAsLabel(getMarkAsLabel(ad))
                .markedAsSold(markAsSold(ad));

        getSellerType(ad).ifPresent(builder::sellerType);
        convertImages(ad, builder);
        convertDates(ad, category, builder);
        return builder.build();
    }

    private boolean markAsSold(Ad ad) {
        return AdStatus.DELETED_USER.equals(ad.getStatus())&&
                DeleteReason.SOLD.equals(ad.getDeleteReason());
    }

    private java.util.Optional<String> getSellerType(Ad ad) {
        if (ad.getAttributes() != null) {
            return Arrays.stream(ad.getAttributes())
                    .filter(at -> at.getName().equals(CategoryConstants.Attribute.SELLER_TYPE.getName()))
                    .map(Attribute::getValue).findFirst();
        }
        return java.util.Optional.empty();
    }

    private boolean isCategoryReadOnly(Optional<Category> category) {
        boolean categoryIsReadOnly = true;
        if (category.isPresent()) {
            categoryIsReadOnly = category.get().getReadOnly();
        }
        return categoryIsReadOnly;
    }

    private String getContactEmail(Ad ad) {
        String contactEmail = "";
        if (ad.getRepliesEmail() != null) {
            contactEmail = ad.getRepliesEmail(); //todo: this might have to be ad.getUserEmail();
        }
        return contactEmail;
    }

    private Boolean isFeatured(Ad ad) {
        Boolean featured = false;
        if (ad.getFeatured() != null) {
            featured = ad.getFeatured();
        }
        return featured;
    }

    private Boolean isUrgent(Ad ad) {
        Boolean urgent = false;
        if (ad.getUrgent() != null) {
            urgent = ad.getUrgent();
        }
        return urgent;
    }

    private String getDisplayLocationText(Ad ad) {
        String displayLocationText = "";
        if (ad.getLocationId() != null) {
            Integer locationId = ad.getLocationId().intValue();
            displayLocationText = getDisplayLocationText(ad, locationService.getById(locationId));
        }
        return displayLocationText;
    }

    private String getMarkAsLabel(Ad ad) {
        Optional<Category> l1CategoryOpt = categoryService.getL1Category(ad.getCategoryId());
        if (l1CategoryOpt.isPresent()) {
            if (Categories.FLATS_AND_HOUSES.is(l1CategoryOpt)) {
                return "No Longer Available";
            }
            if (Categories.SERVICES.is(l1CategoryOpt)) {
                return "No Longer Available";
            }
            if (Categories.JOBS.is(l1CategoryOpt)) {
                return "No Longer Available";
            }
            if (Categories.COMMUNITY.is(l1CategoryOpt)) {
                return "No Longer Available";
            }
        }
        return "Sold";
    }

    private void convertDates(Ad ad, Optional<Category> category, AdPreviewImpl.Builder builder) {
        Date now = new Date();
        if (category.isPresent()) {
            builder.displayDate(getDateLabel(ad, category.get())).seoTimeStamp(getSeoTimeStamp(ad));
        }

        if (ad.getCreationDate() != null) {
            builder.createdTimeAsDate(ad.getCreationDate().toDate())
                    .createdTime(PostingTimeFormatter.formatPostingTime(ad.getCreationDate().toDate(), now));
        }
        if (ad.getLastModifiedDate() != null) {
            builder.lastModifiedTimeAsDate(ad.getLastModifiedDate().toDate())
                    .lastModifiedTime(PostingTimeFormatter.formatPostingTime(ad.getLastModifiedDate().toDate(), now));
        }

        if (ad.getLiveDate() != null) {
            builder.postedTime(PostingTimeFormatter.formatPostingTime(ad.getLiveDate().toDate(), now))
                    .postedTimeAsDate(ad.getLiveDate().toDate());
            builder.publishedRecently(ad.isRecentlyPublished());
        }
    }

    private void convertImages(Ad ad, AdPreviewImpl.Builder builder) {
        String previewUrl = "";
        String thumbUrl = "";
        List<Map<String, LegacyImage>> images = ad.getImages();
        if (images != null && !images.isEmpty() && ad.getMainImageId() != null) {

            builder.numberOfImages(images.size());

            Map<String, LegacyImage> image = Iterables.find(images, new MainImageMatcher(ad.getMainImageId()));

            if (image != null) {

                if (image.containsKey(ImageSize.THUMB.getName())) {
                    LegacyImage legacyImage = image.get(ImageSize.THUMB.getName());
                    previewUrl = cdnImageUrlProvider.getSecureImageUrl(legacyImage.getUrl(), ImageSize.THUMB.getId());
                }

                if (image.containsKey(ImageSize.MINITHUMB.getName())) {
                    LegacyImage legacyImage = image.get(ImageSize.MINITHUMB.getName());
                    thumbUrl = cdnImageUrlProvider.getSecureImageUrl(legacyImage.getUrl(), ImageSize.MINITHUMB.getId());
                }
            }
        }

        builder.previewUrl(previewUrl).thumbUrl(thumbUrl);
    }

    private String getDisplayLocationText(Ad ad, Location location) {
        Location landingLocation = locationService.getLanding(location);
        String locationText = ad.getLocationString();
        if (StringUtils.hasText(locationText)) {
            return LocationDisplayUtils.getCleanedLocationText(locationText);
        }

        if (landingLocation != null) {
            return landingLocation.getDisplayName();
        }

        return "";
    }

    private String getDateLabel(Ad ad, Category category) {
        List<String> dateAttributeNames = getDateAttributeNamesForCategory(category);
        if (ad.getAttributes() != null) {
            for (Attribute attribute : ad.getAttributes()) {
                if (dateAttributeNames.contains(attribute.getName())) {
                    AttributeValue dateValue = null;
                    try {
                        dateValue = DateValue.create(attribute.getValue());
                    } catch (InvalidAttributeValueException e) {
                        e.printStackTrace();
                    }

                    DefaultAttribute newAttribute = new DefaultAttribute(attribute.getName(), dateValue);

                    Optional<DisplayAttribute> displayAttributeOption =
                            attributeService.getDisplayAttribute(newAttribute, category.getId());
                    if (displayAttributeOption.isPresent()) {
                        DisplayAttribute displayAttribute = displayAttributeOption.get();
                        return displayAttribute.getTypeDisplayName() + ": " + displayAttribute.getDisplayValue();
                    }
                }
            }
        }

        return null;
    }

    private List<String> getDateAttributeNamesForCategory(Category category) {
        List<String> dateAttributeNames = Lists.newArrayList();
        for (AttributeMetadata attr : category.getAttributeMetadata()) {
            if (attr.getType() == AttributeType.DATETIME) {
                dateAttributeNames.add(attr.getName());
            }
        }
        return dateAttributeNames;
    }

    private String getSeoTimeStamp(Ad ad) {
        if (ad.getLiveDate() != null) {
            Date liveDate = ad.getLiveDate().toDate();
            if (liveDate == null) {
                return null;
            }

            return dateTimeFormatter.print(liveDate.getTime());
        }

        return null;

    }

    /**
     * For finding main image from list of images
     */
    private final class MainImageMatcher implements Predicate<Map<String, LegacyImage>> {

        private Long mainImageId;

        /**
         * Constructor.
         *
         * @param mainImageId the main image id to find
         */
        private MainImageMatcher(Long mainImageId) {
            this.mainImageId = mainImageId;
        }

        @Override
        public boolean apply(@Nullable Map<String, LegacyImage> input) {
            if (input.size() > 0) {
                List<LegacyImage> legacyImages = new ArrayList<LegacyImage>((input.values()));
                return legacyImages.get(0).getId().equals(mainImageId);
            }
            return false;
        }
    }
}
