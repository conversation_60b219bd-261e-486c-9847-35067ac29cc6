package com.gumtree.web.seller.page.adstats.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.common.util.time.Clock;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.seller.converter.AdToAdPreviewConverter;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.PageNotFoundException;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.service.adstats.AdvertStatisticData;
import com.gumtree.web.seller.service.adstats.AdvertStatsService;
import com.gumtree.web.seller.service.adstats.adcounter.AdCounters;
import com.gumtree.web.view.CSVView;
import com.gumtree.zeno.core.domain.PageType;
import org.jboss.resteasy.client.ClientResponseFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestParam;

import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static java.lang.String.format;

@Controller
@GumtreePage(PageType.StatsAd)
public final class ManageAdStatsController extends BaseSellerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManageAdStatsController.class);

    private static final String STATS_FILE_NAME_KEY = "stats-file-name";
    private static final String STATS_DATA_KEY = "stats";

    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private BushfireApi bushfireApi;

    private Clock clock;

    private AdToAdPreviewConverter converter;

    private UserSession userSession;

    private AdvertStatsService advertStatsService;

    private final CSVView statsCSVView = new CSVView<>(STATS_DATA_KEY, STATS_FILE_NAME_KEY,
            new AdvertStatisticDataCSVConverter()
    );

    @Autowired
    public ManageAdStatsController(
            CookieResolver cookieResolver,
            CategoryModel categoryModel,
            ApiCallExecutor apiCallExecutor,
            ErrorMessageResolver messageResolver,
            UrlScheme urlScheme,
            UserSessionService userSessionService,
            AdvertStatsService advertStatsService,
            BushfireApi bushfireApi,
            Clock clock,
            AdToAdPreviewConverter converter,
            UserSession userSession) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.advertStatsService = advertStatsService;
        this.bushfireApi = bushfireApi;
        this.clock = clock;
        this.converter = converter;
        this.userSession = userSession;
    }

    @RequestMapping(value = "/manage/ads/ad-stats/{id}", method = RequestMethod.GET)
    public ModelAndView showAdStatistics(@PathVariable("id") Long advertId, HttpServletRequest request) {

        Ad advert = getAd(advertId);

        if (advert != null && accountCanEditAd(advert.getAccountId())) {
            ManageAdStatsModel.Builder modelBuilder = ManageAdStatsModel.builder();
            modelBuilder.withAdvert(converter.convert(advert));
            try {
                AdvertStatisticData statisticForAdvert = advertStatsService.getStatisticForAdvert(advertId);
                modelBuilder.withAdStats(statisticForAdvert);
            } catch (Exception e) {
                LOGGER.error("Could not retrieve Ad Stats for Ad Id: {}", advertId, e);
            }
            CoreModel.Builder coreModel = getCoreModelBuilder(request);
            return modelBuilder.build(coreModel);
        } else {
            throw new PageNotFoundException("/manage/ads/ad-stats/" + advertId);
        }
    }

    @RequestMapping(value = "/manage/ads/ad-stats/{id}.json", method = RequestMethod.GET)
    @ResponseBody
    public AdvertStatisticData showAdStatisticsAsJSON(@PathVariable("id") Long advertId) {

        Ad advert = getAd(advertId);

        if (advert != null && accountCanEditAd(advert.getAccountId())) {
            try {
                return advertStatsService.getStatisticForAdvert(advertId);
            } catch (Exception e) {
                LOGGER.error("Could not retrieve Ad Stats for Ad Id:" + advertId, e);
                return null;
            }
        } else {
            throw new PageNotFoundException("/manage/ads/ad-stats/" + advertId + ".json");
        }
    }

    @RequestMapping(value = "/manage/ads/download-ad-stats", method = RequestMethod.GET)
    public ModelAndView downloadStatisticsForAllAds() {
        try {
            Map<String, Object> model = new HashMap<>();
            model.put(STATS_DATA_KEY, advertStatsService.getStatisticForAccount(getAccountId()));
            model.put(STATS_FILE_NAME_KEY, format("advert-stats-%s", dateFormat.format(clock.getDate())));
            return new ModelAndView(statsCSVView, model);
        } catch (Exception e) {
            LOGGER.error("Exception while downloading stats ", e);
            throw e;
        }
    }

    @RequestMapping(value = "/manage/ads/daily/ad-stats/{id}", method = RequestMethod.GET)
    @ResponseBody
    public Map<String, AdCounters> getAdCountersHistory(@PathVariable("id") Long advertId, @RequestParam(value = "days", required = false) Integer days) {
        Ad advert = getAd(advertId);
        if (advert != null && accountCanEditAd(advert.getAccountId())) {
            try {
                return advertStatsService.getStatisticForAdvert(advertId, days);
            } catch (Exception e) {
                LOGGER.error("Could not retrieve Ad Counter History for Ad Id : {} Error : {}", advertId, e);
                return Collections.emptyMap();
            }
        } else {
            throw new PageNotFoundException("/manage/ads/ad-stats/" + advertId);
        }
    }

    private Ad getAd(Long advertId) {
        try {
            return bushfireApi.create(AdvertApi.class).getAdvert(advertId);
        } catch (ClientResponseFailure ex) {
            throw new PageNotFoundException("no ad found for id " + advertId);
        }
    }

    private Long getAccountId() {
        return userSession.getSelectedAccountId();
    }

    private boolean accountCanEditAd(Long accountId) {
        return getAccountId() != null && getAccountId().equals(accountId);
    }
}
