package com.gumtree.web.seller.page.common.model;

import org.springframework.web.servlet.ModelAndView;

public final class Error404Model extends CommonModel {

    private Error404Model(CoreModel core, Builder builder) {
        super(core);
    }

    public static Builder builder() {
        return new Builder();
    }

    public static final class Builder {

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.Error404;
            coreModelBuilder.withTitle("404");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new Error404Model(coreModelBuilder.build(page), this));
        }

    }
}
