package com.gumtree.web.seller.page.login.controller;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.common.properties.GtProps;
import com.gumtree.common.properties.utils.PropSupplier;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.common.util.error.ReportableErrorsArgumentsImpl;
import com.gumtree.config.CommonProperty;
import com.gumtree.config.SellerProperty;
import com.gumtree.util.model.Actions;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.CookieUtils;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.cookie.cutters.LoginCallbackCookie;
import com.gumtree.web.cookie.cutters.threatmetrix.ThreatMetrixCookie;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrix;
import com.gumtree.web.reporting.threatmetrix.ThreatMetrixTracking;
import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.login.LoginFailure;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.RedirectUtils;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.GaElement;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.login.model.LoginModel;
import com.gumtree.web.seller.page.login.reporting.LoginGoogleAnalyticsConfigurer;
import com.gumtree.web.seller.page.login.type.LoginPageResolverData;
import com.gumtree.web.seller.page.login.type.LoginPageResolverUtil;
import com.gumtree.web.seller.page.registration.ResendActivationEmailPageController;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.service.ZenoService;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.metrics.AuthenticationMetrics.incrementCounter;
import static com.gumtree.web.security.login.LoginFailure.INVALID_USERNAME_ERROR;
import static com.gumtree.web.security.login.LoginFailure.RECAPTCHA_CHECK_FAILURE;
import static com.gumtree.web.security.login.LoginFailure.USER_ACCOUNT_NOT_ACTIVE;
import static com.gumtree.web.security.login.LoginFailure.USER_EXISTS_ERROR;
import static com.gumtree.web.security.login.LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE;
import static com.gumtree.web.seller.page.login.controller.LoginController.PAGE_PATH;

@Controller
@RequestMapping(PAGE_PATH)
@GumtreePage(PageType.Login)
@GoogleAnalytics(configurer = LoginGoogleAnalyticsConfigurer.class, contextConfigured = true)
public final class LoginController extends BaseSellerController {

    public static final String PAGE_PATH = "/login";
    public static final String LOGIN_FAILURE = "Login failure";

    private PropSupplier<String> facebookAppId = GtProps.getDStr(SellerProperty.FACEBOOK_APP_ID);
    private PropSupplier<String> googleAppId = GtProps.getDStr(SellerProperty.GOOGLE_APP_ID);

    private final PropSupplier<Boolean> recaptchaEnabled = GtProps.getDBool(SellerProperty.RECAPTCHA_ENABLED);

    private final String recaptchaSiteKey = GtProps.getStr(CommonProperty.RECAPTCHA_SITE_KEY);

    private static final String ATTRIBUTES_VRN_PARAM = "attributes[vrn]";

    private final MeterRegistry meterRegistry;

    private final LoginUtils loginUtils;

    private final ZenoService zenoService;

    private final SecurityHelper securityHelper;

    private final LoginPageResolverUtil loginPageResolverUtil;

    @Value("${gumtree.threatmetrix.orgId:njrya493}")
    private String organisationId;

    @Value("${gumtree.threatmetrix.webBaseUrl}")
    private String webBaseUrl;

    @Value("${gumtree.threatmetrix.enabled:false}")
    private boolean enabled;

    @Autowired
    public LoginController(CookieResolver cookieResolver,
                           CategoryModel categoryModel,
                           ApiCallExecutor apiCallExecutor,
                           ErrorMessageResolver messageResolver,
                           UrlScheme urlScheme,
                           LoginUtils loginUtils,
                           ZenoService zenoService,
                           UserSessionService userSessionService,
                           SecurityHelper securityHelper,
                           LoginPageResolverUtil loginPageResolverUtil,
                           MeterRegistry meterRegistry) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.loginUtils = loginUtils;
        this.zenoService = zenoService;
        this.securityHelper = securityHelper;
        this.loginPageResolverUtil = loginPageResolverUtil;
        this.meterRegistry = meterRegistry;
    }

    /**
     * @return the login form action
     */
    @ModelAttribute("loginFormAction")
    public String loginFormAction() {
        return PAGE_PATH;
    }

    /**
     * Show the login page.
     *
     * @param subject the subject
     * @param email   optional field coming from request parameter (Activation Failure - Scenario 4 GTPA-736)
     * @return login page template name.
     */
    @ThreatMetrix
    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView showLoginPage(Subject subject,
                                      @RequestParam(value = "email", required = false) String email,
                                      @RequestParam(value = "recaptcha", required = false, defaultValue = "false") boolean withRecaptcha,
                                      HttpServletRequest request) {

        if ((subject.isAuthenticated() || subject.isRemembered()) && securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject)) {
            return handleAuthenticatedUser(request);
        }
        return showLoginPage(request, email, withRecaptcha, false);
    }


    @ThreatMetrix
    @RequestMapping(value = "/tmx-info",method = RequestMethod.GET)
    @ResponseBody
    public String setTmxSessionInfo(HttpServletRequest request,HttpServletResponse response) {

        ThreatMetrixCookie tmCookie = cookieResolver.resolve(request, ThreatMetrixCookie.class);
        ThreatMetrixTracking tracking = ThreatMetrixTracking.builder()
                .orgId(organisationId)
                .sessionId(tmCookie.getDefaultValue())
                .webBaseUrl(webBaseUrl)
                .build();
        HashMap<String, Object> map = new HashMap<>();
        map.put("threatMetrixTracking", tracking);

        Cookie cookie = CookieUtils.createHttpCookie(tmCookie.getName(), tmCookie);
        response.addCookie(cookie);
        return new Gson().toJson(map);
    }

    /**
     * Show the post ad login page.
     *
     * @param subject the subject
     * @param email   optional field coming from request parameter (Activation Failure - Scenario 4 GTPA-736)
     * @return login page template name.
     */
    @ThreatMetrix
    @RequestMapping(value = "/postad", method = RequestMethod.GET)
    public ModelAndView showPostAdLoginPage(Subject subject,
                                      @RequestParam(value = "email", required = false) String email,
                                      @RequestParam(value = "recaptcha", required = false, defaultValue = "false") boolean withRecaptcha,
                                      HttpServletRequest request) {

        if ((subject.isAuthenticated() || subject.isRemembered()) && securityHelper.verifyAccessTokenAndLogoutIfInvalid(subject)) {
            return handleAuthenticatedUser(request);
        }
        return showLoginPage(request, email, withRecaptcha, true);
    }

    /**
     * The security framework, Shiro, handles login attempts via its filter that intercepts the request before it gets
     * here. If the request is authenticated by Shiro, it will redirect prior to reaching this method - thus, we are
     * only likely to arrive here following an unsuccessful attempt to login.
     * <p/>
     * TODO: Should probably check if we are already logged in (need to check Shiro docs)
     *
     * @param loginForm    the posted form data
     * @param loginFailure the reason why we got here - i.e. how come Shiro didn't log us in?
     * @param request      the http request
     * @return view name
     * @throws IOException when error occurs trying to redirect
     */
    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView loginFailed(@ModelAttribute("loginForm") LoginForm loginForm,
                                    LoginFailure loginFailure,
                                    HttpServletRequest request) throws IOException {

        if (loginFailure != null) {
            recordFailureOnMetrics(loginForm, loginFailure);

            if (INVALID_USERNAME_ERROR.equals(loginFailure)) {
                populateErrors(loginForm, loginFailure);

            } else if (USER_ACCOUNT_NOT_ACTIVE.equals(loginFailure)) {
                // If the attempt was by a user awaiting activation, we need to display a link to the resend email
                // Note that a trailing slash is required here as Spring MVC doesn't like dots
                ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
                args.add(INACTIVE_USER_MESSAGE_CODE,
                        ResendActivationEmailPageController.PAGE_PATH + "/" + loginForm.getUsername() + "/");
                loginForm.setNewUser(true);
                populateErrors(loginForm, loginFailure, args);

            } else if (RECAPTCHA_CHECK_FAILURE.equals(loginFailure)) {
                populateErrors(loginForm, loginFailure);
            } else {
                // GTPA-746 If the attempt was a new user trying to login with an existing email address
                if (USER_EXISTS_ERROR.equals(loginFailure)) {
                    loginForm.setNewUser(false);
                }
                populateErrors(loginForm, loginFailure);
            }
        }
        if(!loginForm.getLegacy()) {
            throw new FormValidationException(LOGIN_FAILURE, loginForm.getFormErrors());
        }

        CoreModel.Builder coreModel = getCoreModelBuilder(request)
                .withIndex();

        GaElement.Builder gaElementBuilder = GaElement.builder("LoginFailEvent");
        RedirectUtils.getHttpPostPayload().flatMap(extractVrmParameter()).ifPresent(payload -> gaElementBuilder.withLabel("VrmWidget"));

        GaElement gaElement = gaElementBuilder.build();
        coreModel.withGaEventElements(Collections.singletonList(gaElement));

        LoginCallbackCookie loginCallbackCookie = cookieResolver.resolve(request, LoginCallbackCookie.class);
        LoginModel.Builder loginModel = LoginModel.builder()
                .withLoginForm(loginForm)
                .withRecaptchaEnabled(recaptchaEnabled.get())
                .withRecaptchaSiteKey(recaptchaSiteKey)
                .withFacebookAppId(facebookAppId.get())
                .withGoogleAppId(googleAppId.get())
                .withLoginToContact(isRedirectedFromAContactForm(loginCallbackCookie))
                .withPage(getLoginPage(false));
        return loginModel.build(coreModel);
    }

    private ModelAndView showLoginPage(HttpServletRequest request, String email,
                                       boolean withRecaptcha, boolean isPostAdLogin) {

        LoginCallbackCookie loginCallbackCookie = cookieResolver.resolve(request, LoginCallbackCookie.class);
        RedirectUtils.saveCallbackURL(request, loginCallbackCookie);

        LoginForm loginForm = new LoginForm();
        if (email == null) {
            email = loginUtils.getNewUserEmailAddressFromSession();
        }
        loginForm.setUsername(email);

        CoreModel.Builder coreModel = getCoreModelBuilder(request)
                .withIndex();

        GaElement.Builder gaElementBuilder = GaElement.builder("LoginBeginEvent");

        RedirectUtils.getHttpPostPayload().flatMap(extractVrmParameter()).ifPresent(__ -> gaElementBuilder.withLabel("VrmWidget"));
        GaElement gaElement = gaElementBuilder.build();
        coreModel.withGaEventElements(Collections.singletonList(gaElement));

        LoginModel.Builder loginModel = LoginModel.builder()
                .withLoginForm(loginForm)
                .withRecaptchaEnabled(recaptchaEnabled.get() || withRecaptcha)
                .withRecaptchaSiteKey(recaptchaSiteKey)
                .withFacebookAppId(facebookAppId.get())
                .withGoogleAppId(googleAppId.get())
                .withLoginToContact(isRedirectedFromAContactForm(loginCallbackCookie))
                .withPage(getLoginPage(isPostAdLogin));
        return loginModel.build(coreModel);
    }

    private Function<String, Optional<String>> extractVrmParameter() {
        return payload -> URLEncodedUtils.parse(payload, StandardCharsets.UTF_8).stream()
                .filter(queryParam -> queryParam.getName().equals(ATTRIBUTES_VRN_PARAM))
                .findFirst()
                .map(NameValuePair::getValue);
    }

    private Page getLoginPage(boolean showPostAdLogin) {
        if (showPostAdLogin) {
            return Page.Login_Post_Ad;
        }
        LoginPageResolverData resolverData = populateContextWithSavedRequest(LoginPageResolverData.builder()).build();
        return loginPageResolverUtil.resolve(resolverData);
    }

    private LoginPageResolverData.Builder populateContextWithSavedRequest(LoginPageResolverData.Builder resolverDataBuilder) {
         Optional.ofNullable(WebUtils.getSavedRequest(null))
                 .ifPresent(savedRequest -> {
                     resolverDataBuilder.withSavedRequestURI(savedRequest.getRequestURI());
                     resolverDataBuilder.withSavedQueryString(savedRequest.getQueryString());
                 });
         return resolverDataBuilder;
    }

    private void recordFailureOnMetrics(LoginForm loginForm, LoginFailure loginFailure) {
        Map<String, String> failureParams = Maps.newHashMap();
        failureParams.put("email", loginForm.getUsername());
        failureParams.put("reason", loginFailure.name());
        zenoService.logBackendEvent("LoginFailEvent", failureParams);
        incrementCounter(meterRegistry, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    private ModelAndView handleAuthenticatedUser(HttpServletRequest request) {
        Optional<String> callbackURL = RedirectUtils.getCallbackURL(request);
        if (callbackURL.isPresent()) {
            return redirect(callbackURL.get());
        }

        return redirect(getUrlScheme().urlFor(Actions.BUSHFIRE_MANAGE_ADS));
    }

    private boolean isRedirectedFrom(String originalContext, LoginCallbackCookie loginCallbackCookie) {
        Optional<String> callbackURL = RedirectUtils.getSavedCallbackURL(loginCallbackCookie);
        return callbackURL
                .map(cb -> cb.contains(originalContext))
                .orElse(false);
    }

    private boolean isRedirectedFromAContactForm(LoginCallbackCookie loginCallbackCookie) {
        return isRedirectedFrom("reply", loginCallbackCookie) || isRedirectedFrom("reveal", loginCallbackCookie);
    }
}


