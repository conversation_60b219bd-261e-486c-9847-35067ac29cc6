package com.gumtree.web.seller.page.manageads.model;

import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;

import java.io.Serializable;

/**
 * Represents a checkout within post/manage ads.
 * <p>
 * TODO: Not sure a checkout should be mutable - i.e. once it exists, id and order id should be fixed?
 */
public interface Checkout extends Serializable {

    /**
     * @return the unique id of the checkout
     */
    String getKey();

    /**
     * Set the checkout id.
     *
     * @param id the checkout id
     */
    void setKey(String id);

    /**
     * Set the order id.
     *
     * @param order the order
     */
    void setOrder(ApiOrder order);

    /**
     * @return the order id associated with this checkout
     */
    ApiOrder getOrder();

    /**
     * Set whether this checkout resulted from a create or edit of an advert
     *
     * @param v true if the checkout was created in response to an advert create or edit
     */
    void setCreateOrEdit(boolean v);

    /**
     * @return Whether or not the checkout was created in response to a an advert create or edit
     */
    boolean isCreateOrEdit();

    /**
     * Set an advert id
     *
     * @param advert - advert
     */
    void setAdvert(CheckoutAdvert advert);

    /**
     * @return an advert id
     */
    CheckoutAdvert getAdvert();

    /**
     * Set the user's phone number
     *
     * @param phoneNumber - phone number
     */
    void setUserPhone(String phoneNumber);

    /**
     * @return the user set phone number
     */
    String getUserPhone();

    void setMetaPathInfo(MetaPathInfo metaPathInfo);

    MetaPathInfo getMetaPathInfo();

}
