package com.gumtree.web.seller.page.password.controller;

import com.gumtree.api.category.CategoryModel;
import com.gumtree.api.client.executor.ApiCallExecutor;
import com.gumtree.util.url.UrlScheme;
import com.gumtree.web.common.error.ErrorMessageResolver;
import com.gumtree.web.common.error.mvc.MvcReportableErrors;
import com.gumtree.web.common.page.GumtreePage;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.cookie.UserSessionService;
import com.gumtree.web.reporting.google.ga.GoogleAnalytics;
import com.gumtree.web.seller.page.BaseSellerController;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.password.model.ForgottenPasswordModel;
import com.gumtree.web.seller.page.password.model.ForgottenPasswordResult;
import com.gumtree.web.seller.security.ParameterEncryption;
import com.gumtree.web.seller.service.user.forgotpassword.PasswordResetService;
import com.gumtree.zeno.core.domain.PageType;
import com.gumtree.zeno.core.event.user.sellerside.password.PasswordResetBegin;
import com.gumtree.zeno.core.service.ZenoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

import static com.gumtree.web.seller.page.password.controller.ForgottenPasswordController.PAGE_PATH;

@GumtreePage(PageType.PasswordForgotten)
@GoogleAnalytics
@RequestMapping(PAGE_PATH)
@Controller
public final class ForgottenPasswordController extends BaseSellerController {

    public static final String PAGE_PATH = "/forgotten-password";
    public static final String EXPERIMENT_FORGOT_PASSWORD_PATH = "/reset";
    public static final String EMAIL_FLASH_ATTRIBUTES = "emailAddress";

    private final PasswordResetService passwordResetService;

    private final ParameterEncryption parameterEncryption;

    static final String ENCRYPTED_MAP_PARAMETER_NAME = "gt_d";

    @Autowired
    public ForgottenPasswordController(CookieResolver cookieResolver,
                                       CategoryModel categoryModel,
                                       ApiCallExecutor apiCallExecutor,
                                       ErrorMessageResolver messageResolver,
                                       UrlScheme urlScheme,
                                       ZenoService zenoService,
                                       UserSessionService userSessionService,
                                       PasswordResetService passwordResetService,
                                       ParameterEncryption parameterEncryption) {
        super(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme, userSessionService);
        this.passwordResetService = passwordResetService;
        this.zenoService = zenoService;
        this.parameterEncryption = parameterEncryption;
    }

    @RequestMapping(method = RequestMethod.GET)
    public ModelAndView showPage(HttpServletRequest request) {

        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        ForgottenPasswordModel.Builder forgottenPasswordModel = ForgottenPasswordModel.builder()
                .withResetPasswordForm(new ResetPasswordFormBean());

        zenoService.logEvent(new String[0], Page.ForgottenPassword.getTemplateName(), PasswordResetBegin.class);

        return forgottenPasswordModel.build(coreModel);
    }

    @RequestMapping(method = RequestMethod.POST)
    public ModelAndView sendEmail(@ModelAttribute("model") ForgottenPasswordModel incomingModel,
                                  BindingResult bindingResult,
                                  HttpServletRequest request) {
        ResetPasswordFormBean resetPasswordFormBean = incomingModel.getForm();
        CoreModel.Builder coreModel = getCoreModelBuilder(request);
        ForgottenPasswordModel.Builder forgottenPasswordModel = ForgottenPasswordModel.builder()
                .withResetPasswordForm(resetPasswordFormBean);

        if (bindingResult.hasErrors()) {
            populateErrors(resetPasswordFormBean, new MvcReportableErrors(bindingResult));
            return forgottenPasswordModel.build(coreModel);
        }

        passwordResetService.resetPassword(resetPasswordFormBean.getUsername());

        Map<String, String> data = new HashMap<>();
        data.put(EMAIL_FLASH_ATTRIBUTES, resetPasswordFormBean.getUsername());
        String encryptedData = parameterEncryption.encryptMapAndUrlEncode(data);

        return redirectWithParameters(ForgottenPasswordEmailConfirmationController.PAGE_PATH, ENCRYPTED_MAP_PARAMETER_NAME, encryptedData);
    }

    // This has been created to support Login and Registration improvement experiment GTC-2229
    @RequestMapping(value = EXPERIMENT_FORGOT_PASSWORD_PATH, method = RequestMethod.POST)
    public ResponseEntity<ForgottenPasswordResult> sendForgottenPasswordEmail(@ModelAttribute("model")
                                                                                  ForgottenPasswordModel incomingModel,
                                                                          BindingResult bindingResult) {
        ResetPasswordFormBean resetPasswordFormBean = incomingModel.getForm();

        if (bindingResult.hasErrors()) {
            populateErrors(resetPasswordFormBean, new MvcReportableErrors(bindingResult));
            return new ResponseEntity<>(
                    ForgottenPasswordResult.builder()
                            .resetPasswordFormBean(resetPasswordFormBean)
                            .withError(true)
                            .build(),
                    HttpStatus.BAD_REQUEST
            );
        }

        passwordResetService.resetPassword(resetPasswordFormBean.getUsername());

        return new ResponseEntity<>(
                ForgottenPasswordResult.builder()
                        .resetPasswordFormBean(resetPasswordFormBean)
                        .withError(false).build(),
                HttpStatus.OK
        );
    }
}
