package com.gumtree.web.seller.page.postad.common;

import com.gumtree.api.Ad;
import com.gumtree.api.Attribute;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;


public final class AdUtils {

    public static Optional<Attribute> findAttribute(@NotNull Ad ad, @NotNull String name) {
        return ad.getAttributes() != null ?
                Arrays.stream(ad.getAttributes())
                        .filter(att -> name.equals(att.getName()))
                        .findAny()
                : Optional.empty();
    }

    private AdUtils() {
    }
}
