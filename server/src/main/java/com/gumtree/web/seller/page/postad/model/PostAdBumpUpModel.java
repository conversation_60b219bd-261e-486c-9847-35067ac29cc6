package com.gumtree.web.seller.page.postad.model;

import com.gumtree.web.common.error.ReportableErrorsMessageResolvingErrorSource;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.CoreModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.postad.model.products.PricingMetadata;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.List;

public final class PostAdBumpUpModel extends CommonModel {

    private boolean requiresBumpUp;
    private BumpUpFormBean form;
    private String bumpUpFormAction;
    private PricingMetadata pricingMetadata;
    private List<BumpUpOption> bumpUpOptions;
    private String verb;
    private ReportableErrorsMessageResolvingErrorSource errors;
    private String editPageUrl;

    private PostAdBumpUpModel(CoreModel core, Builder builder) {
        super(core);
        this.requiresBumpUp = builder.requiresBumpUp;
        this.form = builder.form;
        this.bumpUpFormAction = builder.bumpUpFormAction;
        this.pricingMetadata = builder.pricingMetadata;
        this.bumpUpOptions = builder.bumpUpOptions;
        this.verb = builder.verb;
        this.errors = builder.errors;
        this.editPageUrl = builder.editPageUrl;
    }

    public BumpUpFormBean getForm() {
        return form;
    }

    public void setForm(BumpUpFormBean form) {
        this.form = form;
    }

    public boolean isRequiresBumpUp() {
        return requiresBumpUp;
    }

    public String getBumpUpFormAction() {
        return bumpUpFormAction;
    }

    public PricingMetadata getPricingMetadata() {
        return pricingMetadata;
    }

    public List<BumpUpOption> getBumpUpOptions() {
        return bumpUpOptions;
    }

    public String getVerb() {
        return verb;
    }

    public static Builder builder() {
        return new Builder();
    }

    public ReportableErrorsMessageResolvingErrorSource getErrors() {
        return errors;
    }

    public void setErrors(ReportableErrorsMessageResolvingErrorSource errors) {
        this.errors = errors;
    }

    public String getEditPageUrl() {
        return editPageUrl;
    }

    public static final class Builder {

        private boolean requiresBumpUp;
        private BumpUpFormBean form;
        private String bumpUpFormAction;
        private PricingMetadata pricingMetadata;
        private List<BumpUpOption> bumpUpOptions;
        private String verb;
        private ReportableErrorsMessageResolvingErrorSource errors;
        private String editPageUrl;

        public Builder withErrors(ReportableErrorsMessageResolvingErrorSource errors) {
            this.errors = errors;
            return this;
        }

        public Builder withRequiresBumpUp(boolean requiresBumpUp) {
            this.requiresBumpUp = requiresBumpUp;
            return this;
        }

        public Builder withForm(BumpUpFormBean form) {
            this.form = form;
            return this;
        }

        public Builder withBumpUpFormAction(String bumpUpFormAction) {
            this.bumpUpFormAction = bumpUpFormAction;
            return this;
        }

        public Builder withPricingMetadata(PricingMetadata pricingMetadata) {
            this.pricingMetadata = pricingMetadata;

            this.bumpUpOptions = Arrays.asList(
                    new BumpUpOption("Bump my ad to the top of the listings - " +
                            pricingMetadata.getBumpUpPrice().getDisplayValue(), "true"),
                    new BumpUpOption("Keep it where it is", "false")
            );

            return this;
        }

        public Builder withVerb(String verb) {
            this.verb = verb;
            return this;
        }

        public Builder withEditPageUrl(String editPageUrl) {
            this.editPageUrl = editPageUrl;
            return this;
        }

        public ModelAndView build(CoreModel.Builder coreModelBuilder) {
            Page page = Page.PostAdBumpUp;
            coreModelBuilder.withTitle("Bump up");
            return new ModelAndView(page.getTemplateName(), CommonModel.MODEL_KEY,
                    new PostAdBumpUpModel(coreModelBuilder.build(page), this));
        }

    }
}
