package com.gumtree.web.seller.page.postad.model.meta;

import com.gumtree.web.seller.page.postad.model.products.ProductType;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 6/15/17.
 */
public class MetaPathInfo {

    private List<ProductType> featureTypes;
    private PageActionType pageActionType;
    private List<PagePaymentType> pagePaymentTypes;
    private boolean isMultipleAds;

    public MetaPathInfo() {
    }

    public MetaPathInfo(List<ProductType> featureTypes,
                        PageActionType pageActionType,
                        List<PagePaymentType> pagePaymentTypes,
                        boolean isMultipleAds) {

        this.featureTypes = featureTypes;
        this.pageActionType = pageActionType;
        this.pagePaymentTypes = pagePaymentTypes;
        this.isMultipleAds = isMultipleAds;
    }

    public MetaPathInfo(List<String> featureTypesString,
                        String pageActionTypeString,
                        List<String> pagePaymentTypesString,
                        boolean isMultipleAds) {
        this.featureTypes = featureTypesString.stream()
                .map(x -> ProductType.valueOf(x)).collect(Collectors.toList());
        this.pageActionType = PageActionType.valueOf(pageActionTypeString);
        this.pagePaymentTypes = pagePaymentTypesString.stream()
                .map(x -> PagePaymentType.valueOf(x)).collect(Collectors.toList());
        this.isMultipleAds = isMultipleAds;
    }

    public List<ProductType> getFeatureTypes() {
        return this.featureTypes;
    }

    public PageActionType getPageActionType() {
        return this.pageActionType;
    }

    public List<PagePaymentType> getPagePaymentTypes() {
        return this.pagePaymentTypes;
    }

    public boolean getIsMultipleAds() {
        return this.isMultipleAds;
    }

    public void setFeatureTypes(List<ProductType> featureTypes) {
        this.featureTypes = featureTypes;
    }

    public void setPageActionType(PageActionType pageActionType) {
        this.pageActionType = pageActionType;
    }

    public void setPagePaymentTypes(List<PagePaymentType> pagePaymentTypes) {
        this.pagePaymentTypes = pagePaymentTypes;
    }

    public void setIsMultipleAds(boolean isMultipleAds) {
        this.isMultipleAds = isMultipleAds;
    }
}
