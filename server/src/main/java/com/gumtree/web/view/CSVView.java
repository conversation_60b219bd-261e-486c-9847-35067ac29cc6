package com.gumtree.web.view;

import com.googlecode.jcsv.CSVStrategy;
import com.googlecode.jcsv.writer.CSVWriter;
import com.googlecode.jcsv.writer.internal.CSVWriterBuilder;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import static java.lang.String.format;
import static org.apache.commons.lang.StringUtils.join;


public class CSVView<T> implements View {

    private final String dataModelKey;
    private final String filenameModelKey;
    private CSVEntryConverter<T> csvEntryConverter;

    /**
     * Create CSV view
     * @param dataModelKey name of the model key for the data for the CSV file, java.util.List is expected to
     *                     be stored under the key
     * @param filenameModelKey name of the model key for the name of the result CSV file, without the extension,
     *                         appropriate extension will be added by the CVSView
     * @param csvEntryConverter converter which converts beans to rows
     */
    public CSVView(String dataModelKey, String filenameModelKey, CSVEntryConverter<T> csvEntryConverter) {
        this.dataModelKey = dataModelKey;
        this.filenameModelKey = filenameModelKey;
        this.csvEntryConverter = csvEntryConverter;
    }

    @Override
    public String getContentType() {
        return "text/csv";
    }

    @Override
    public void render(Map<String, ?> model
                        , HttpServletRequest httpServletRequest
                        , HttpServletResponse httpServletResponse
                       ) throws Exception {
        httpServletResponse.addHeader("Content-disposition"
                                                , format("attachment;filename=%s.csv", model.get(filenameModelKey)));
        httpServletResponse.addHeader("Content-Type", "text/csv");
        PrintWriter writer = httpServletResponse.getWriter();
        //add column headers
        writer.write(join(csvEntryConverter.getColumnNames(), ","));
        writer.write("\r\n");
        //write out data
        List<T> listOfRows = (List<T>) model.get(dataModelKey);
        CSVWriter<T> csvWriter = new CSVWriterBuilder<T>(writer).entryConverter(csvEntryConverter)
                                                                        .strategy(CSVStrategy.UK_DEFAULT).build();
        csvWriter.writeAll(listOfRows);

    }
}
