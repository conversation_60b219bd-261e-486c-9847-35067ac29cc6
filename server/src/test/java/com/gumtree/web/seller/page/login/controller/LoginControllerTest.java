package com.gumtree.web.seller.page.login.controller;

import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.common.util.error.ReportableErrorsArguments;
import com.gumtree.common.util.error.ReportableErrorsArgumentsImpl;
import com.gumtree.gas.OpenIdConnectService;
import com.gumtree.util.model.Actions;
import com.gumtree.web.common.error.MessageSourceErrorMessageResolver;
import com.gumtree.web.cookie.cutters.LoginCallbackCookie;
import com.gumtree.web.security.SecurityHelper;
import com.gumtree.web.security.exception.FormValidationException;
import com.gumtree.web.security.login.LoginFailure;
import com.gumtree.web.security.login.LoginFailureMessageCodes;
import com.gumtree.web.security.login.LoginUtils;
import com.gumtree.web.security.shiro.RedirectUtils;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.common.model.CommonModel;
import com.gumtree.web.seller.page.common.model.Page;
import com.gumtree.web.seller.page.login.model.LoginModel;
import com.gumtree.web.seller.page.login.type.LoginPageResolverData;
import com.gumtree.web.seller.page.login.type.LoginPageResolverUtil;
import com.gumtree.web.seller.page.registration.ResendActivationEmailPageController;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.MockClock;
import io.micrometer.core.instrument.simple.SimpleConfig;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.context.MessageSource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.gumtree.metrics.AuthenticationMetrics.ACTION;
import static com.gumtree.metrics.AuthenticationMetrics.Action;
import static com.gumtree.metrics.AuthenticationMetrics.NAME;
import static com.gumtree.metrics.AuthenticationMetrics.STATUS;
import static com.gumtree.metrics.AuthenticationMetrics.Status;
import static com.gumtree.web.security.login.LoginFailureMessageCodes.UNKNOWN_USERNAME_MESSAGE_CODE;
import static com.gumtree.web.security.shiro.RedirectUtils.HTTP_POST_PAYLOAD;
import static com.gumtree.web.seller.page.login.controller.LoginController.LOGIN_FAILURE;
import static org.fest.assertions.api.Assertions.fail;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

@RunWith(MockitoJUnitRunner.class)
public class LoginControllerTest extends BaseSellerControllerTest {

    private static final String RECAPTCHA_SITE_KEY = "test";

    private LoginController controller;
    private MeterRegistry meterRegistry;

    @Mock
    private LoginUtils loginUtils;

    @Mock
    private HttpServletResponse response;

    @Mock
    private OpenIdConnectService oidcService;

    @Mock
    private SecurityHelper securityHelper;

    @Mock
    private Subject user;

    @Mock
    private Session session;

    @Mock
    private LoginCallbackCookie loginCallbackCookie;

    @Mock
    private LoginPageResolverUtil loginPageResolverUtil;

    @Before
    public void init() throws ConfigurationException {
        GtPropertiesInitializer.init("seller-server");
        initMocks(this);

        when(request.getRequestURL()).thenReturn(new StringBuffer());
        when(request.getHeaderNames()).thenReturn(Collections.<String>emptyEnumeration());
        when(loginPageResolverUtil.resolve(any(LoginPageResolverData.class))).thenReturn(Page.Login);
        meterRegistry = new SimpleMeterRegistry(SimpleConfig.DEFAULT, new MockClock());

        controller = new LoginController(cookieResolver, categoryModel, apiCallExecutor, messageResolver, urlScheme,
                loginUtils, zenoService, userSessionService, securityHelper, loginPageResolverUtil, meterRegistry);


        autowireAbExperimentsService(controller);
        when(user.getSession(false)).thenReturn(session);
        when(cookieResolver.resolve(request, LoginCallbackCookie.class)).thenReturn(loginCallbackCookie);
        when(loginCallbackCookie.getValueOpt()).thenReturn(java.util.Optional.empty());

        setSubject(user);
    }

    @Test
    public void loginPageHasCorrectTemplateNameIfNotInExperimentsVariant() {
        when(session.getAttribute(any(String.class))).thenReturn(null);
        when(user.isAuthenticated()).thenReturn(false);

        assertThat(controller.showLoginPage(user, "", false, request).getViewName(),
                equalTo(Page.Login.getTemplateName()));
    }

    @Test
    public void loginPageHasCorrectTemplateNameIfInExperimentsVariantAndItIsNotPostAd() {
        when(session.getAttribute(any(String.class))).thenReturn(null);
        when(user.isAuthenticated()).thenReturn(false);

        assertThat(controller.showLoginPage(user, "", false, request).getViewName(),
                equalTo(Page.Login.getTemplateName()));
    }

    @Test
    public void loginPageHasCorrectTemplateNameIfIsPostAd() {
        // when - then
        assertThat(controller.showPostAdLoginPage(user, "", false, request).getViewName(),
                equalTo(Page.Login_Post_Ad.getTemplateName()));
    }

    @Test
    public void loginPageRedirectsToManageAdsWhenSubjectIsAuthenticated() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn("/test/url");
        Subject user = mock(Subject.class);
        when(user.isAuthenticated()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(user)).thenReturn(true);

        // when
        View view = controller.showLoginPage(user, "", false, request).getView();

        // then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo("/test/url"));
    }

    @Test
    public void loginPageRedirectsToManageAdsWhenSubjectIsRememberedAndAccessTokenIsValid() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn("/test/url");
        Subject user = mock(Subject.class);
        when(user.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(user)).thenReturn(true);

        // when
        View view = controller.showLoginPage(user, "", false, request).getView();

        // then
        assertTrue(view instanceof RedirectView);
        assertTrue(((RedirectView) view).isRedirectView());
        assertThat(((RedirectView) view).getUrl(), equalTo("/test/url"));
    }

    @Test
    public void loginPageRedirectsToManageAdsWhenSubjectIsRememberedAndAccessTokenIsInvalid() {
        when(urlScheme.urlFor(Actions.BUSHFIRE_MANAGE_ADS)).thenReturn("/test/url");
        Subject user = mock(Subject.class);
        Session session = mock(Session.class);
        when(user.isRemembered()).thenReturn(true);
        when(securityHelper.verifyAccessTokenAndLogoutIfInvalid(user)).thenReturn(false);
        when(user.getSession()).thenReturn(session);
        when(session.getAttribute(any(String.class))).thenReturn(null);

        // when
        ModelAndView view = controller.showLoginPage(user, "", false, request);

        // then
        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));
        LoginModel model = (LoginModel) view.getModel().get(CommonModel.MODEL_KEY);
        assertNotNull(model);
        assertEquals(model.getRecaptchaSiteKey(), RECAPTCHA_SITE_KEY);
    }

    // In theory, the system should never get in this state.
    @Test
    public void whenLoginFailureIsNullJustReturnsToLoginPage() throws IOException {
        LoginForm loginForm = new LoginForm();
        ModelAndView view = controller.loginFailed(loginForm, null, request);
        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));

        LoginModel model = (LoginModel) view.getModel().get(CommonModel.MODEL_KEY);
        assertNotNull(model);
        assertEquals(model.getRecaptchaSiteKey(), RECAPTCHA_SITE_KEY);
    }

    @Test
    public void whenLoginFailureIsNullForWebBffIntegration() throws IOException {
        LoginForm loginForm = new LoginForm();
        loginForm.setLegacy(false);
        assertThrows(LOGIN_FAILURE, FormValidationException.class,
                 () -> controller.loginFailed(loginForm, null, request));
    }
    @Test
    public void whenLoginFailureIsUserExistsThenEnsureLoginTypeIsForcedToBeExistingUser() throws Exception {
        LoginForm loginForm = new LoginForm();
        loginForm.setNewUser(true);
        ModelAndView view = controller.loginFailed(loginForm, LoginFailure.USER_EXISTS_ERROR, request);
        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));
        assertThat(loginForm.getNewUser(), equalTo(false));

        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void whenLoginFailureIsUserExistsThenEnsureLoginTypeIsForcedToBeExistingUserInWebBffIntegration() throws Exception {
        LoginForm loginForm = new LoginForm();
        loginForm.setNewUser(true);
        loginForm.setLegacy(false);
        try {
            controller.loginFailed(loginForm, LoginFailure.USER_EXISTS_ERROR, request);
            fail("FormValidation exception should have been thrown");
        } catch (FormValidationException ex) {
            assertThat(ex.getFormErrors().containsKey("username"), equalTo(true));
        }
        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void whenLoginFailureIsUserNotActivatedThenEnsureErrorContainsResendLink() throws Exception {

        LoginForm loginForm = new LoginForm();
        loginForm.setUsername("<EMAIL>");
        LoginFailure loginFailure = LoginFailure.USER_ACCOUNT_NOT_ACTIVE;
        ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
        args.add(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE,
                ResendActivationEmailPageController.PAGE_PATH + "/" + loginForm.getUsername() + "/");

        MessageSource mockMessageSource = mock(MessageSource.class);
        ReflectionTestUtils
                .setField(controller, "messageResolver", new MessageSourceErrorMessageResolver(mockMessageSource));
        when(mockMessageSource.getMessage(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE,
                args.getArguments(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE),
                LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE, Locale.getDefault()))
                .thenReturn(
                        "[resend link](" + ResendActivationEmailPageController.PAGE_PATH + "/<EMAIL>/)");
        ModelAndView view = controller.loginFailed(loginForm, loginFailure, request);

        LoginModel model = (LoginModel) view.getModel().get(CommonModel.MODEL_KEY);
        assertNotNull(model);
        assertThat(model.getForm().getFormErrors().containsKey("username"), equalTo(true));
        assertEquals(model.getRecaptchaSiteKey(), RECAPTCHA_SITE_KEY);

        List<String> usernameErrors = model.getForm().getFormErrors().get("username");
        assertThat(usernameErrors.contains(
                "[resend link](" + ResendActivationEmailPageController.PAGE_PATH + "/<EMAIL>/)"),
                equalTo(true));
        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));

        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void whenLoginFailureIsUserNotActivatedThenEnsureErrorContainsResendLinkForWebBffIntegration() throws Exception {

        LoginForm loginForm = new LoginForm();
        loginForm.setLegacy(false);
        loginForm.setUsername("<EMAIL>");
        LoginFailure loginFailure = LoginFailure.USER_ACCOUNT_NOT_ACTIVE;
        ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
        args.add(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE,
                ResendActivationEmailPageController.PAGE_PATH + "/" + loginForm.getUsername() + "/");

        MessageSource mockMessageSource = mock(MessageSource.class);
        ReflectionTestUtils
                .setField(controller, "messageResolver", new MessageSourceErrorMessageResolver(mockMessageSource));
        when(mockMessageSource.getMessage(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE,
                args.getArguments(LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE),
                LoginFailureMessageCodes.INACTIVE_USER_MESSAGE_CODE, Locale.getDefault()))
                .thenReturn(
                        "[resend link](" + ResendActivationEmailPageController.PAGE_PATH + "/<EMAIL>/)");

        try {
            controller.loginFailed(loginForm, loginFailure, request);
            fail("FormValidation exception should have been thrown");
        } catch(FormValidationException ex) {
            assertThat(ex.getFormErrors().containsKey("username"), equalTo(true));
            List<String> usernameErrors = ex.getFormErrors().get("username");
            assertThat(usernameErrors.contains(
                            "[resend link](" + ResendActivationEmailPageController.PAGE_PATH + "/<EMAIL>/)"),
                    equalTo(true));
        }
        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void handlesUnknownAccountFailureLoggingIntoBushfire() throws IOException {
        when(urlScheme.urlFor(Actions.CREATE_ACCOUNT)).thenReturn("redirectURL");
        LoginForm loginForm = new LoginForm();
        LoginFailure loginFailure = LoginFailure.UNKNOWN_ACCOUNT;
        ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
        args.add(UNKNOWN_USERNAME_MESSAGE_CODE, urlScheme.urlFor(Actions.CREATE_ACCOUNT));
        ModelAndView view = controller.loginFailed(loginForm, loginFailure, request);

        LoginModel model = (LoginModel) view.getModel().get(CommonModel.MODEL_KEY);
        assertNotNull(model);
        assertEquals(model.getRecaptchaSiteKey(), RECAPTCHA_SITE_KEY);

        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));
        assertThat(loginForm.getNewUser(), equalTo(false));
    }

    @Test
    public void handlesUnknownAccountFailureLoggingIntoBushfireInWebBffIntegration() throws IOException {
        when(urlScheme.urlFor(Actions.CREATE_ACCOUNT)).thenReturn("redirectURL");
        LoginForm loginForm = new LoginForm();
        loginForm.setLegacy(false);
        LoginFailure loginFailure = LoginFailure.UNKNOWN_ACCOUNT;
        ReportableErrorsArguments args = ReportableErrorsArgumentsImpl.getInstance();
        args.add(UNKNOWN_USERNAME_MESSAGE_CODE, urlScheme.urlFor(Actions.CREATE_ACCOUNT));
        try {
            controller.loginFailed(loginForm, loginFailure, request);
            fail("FormValidation exception should have been thrown");
        } catch(FormValidationException ex) {
            assertThat(ex.getFormErrors().containsKey("username"), equalTo(true));
            assertThat(ex.getFormErrors().containsKey("password"), equalTo(true));
        }
    }

    @Test
    public void handlesABushfireLoginFailureErrorCorrectly() throws IOException {
        LoginForm loginForm = new LoginForm();
        LoginFailure loginFailure = LoginFailure.INCORRECT_CREDENTIALS;
        ModelAndView view = controller.loginFailed(loginForm, loginFailure, request);

        LoginModel model = (LoginModel) view.getModel().get(CommonModel.MODEL_KEY);
        assertThat(model.getForm().getFormErrors().containsKey("password"), equalTo(true));
        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));

        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void handlesABushfireLoginFailureErrorCorrectlyForWebBffIntegration() throws IOException {
        LoginForm loginForm = new LoginForm();
        loginForm.setLegacy(false);
        LoginFailure loginFailure = LoginFailure.INCORRECT_CREDENTIALS;

        try {
            controller.loginFailed(loginForm, loginFailure, request);
            fail("FormValidation exception should have been thrown");
        } catch(FormValidationException ex) {
            assertThat(ex.getFormErrors().containsKey("username"), equalTo(true));
            assertThat(ex.getFormErrors().containsKey("password"), equalTo(true));
        }
        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void logsFailedLoginToZeno() throws IOException {
        LoginForm loginForm = new LoginForm();
        loginForm.setUsername("<EMAIL>");
        ModelAndView view = controller.loginFailed(loginForm, LoginFailure.MISSING_PASSWORD, request);
        ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);

        verify(zenoService).logBackendEvent(eq("LoginFailEvent"), captor.capture());
        Map<String, String> map = captor.getValue();

        assertThat(map.size(), equalTo(2));
        assertThat(map.get("email"), equalTo("<EMAIL>"));
        assertThat(map.get("reason"), equalTo("MISSING_PASSWORD"));

        assertThat(view.getViewName(), equalTo(Page.Login.getTemplateName()));
        assertThat(loginForm.getNewUser(), equalTo(false));

        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void logsFailedLoginToZenoForWebBffIntegration() throws IOException {
        LoginForm loginForm = new LoginForm();
        loginForm.setLegacy(false);
        loginForm.setUsername("<EMAIL>");
        try {
            controller.loginFailed(loginForm, LoginFailure.MISSING_PASSWORD, request);
            fail("FormValidation exception should have been thrown");
        } catch(FormValidationException ex) {
            ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
            verify(zenoService).logBackendEvent(eq("LoginFailEvent"), captor.capture());
            Map<String, String> map = captor.getValue();

            assertThat(map.size(), equalTo(2));
            assertThat(map.get("email"), equalTo("<EMAIL>"));
            assertThat(map.get("reason"), equalTo("MISSING_PASSWORD"));
            assertThat(ex.getFormErrors().containsKey("password"), equalTo(true));
        }
        assertMetricValue(1, Action.GUMTREE_LOGIN, Status.FAILURE);
    }

    @Test
    public void whenRedirectedFromVRMWidgetTheGAEventLabelShouldBeVRMWidget() throws IOException {
        //        Given
        String expectedPayload = "attributes[vrn]=W90MOG&categoryId=1991";
        when(request.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(request.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(request.getRequestURI()).thenReturn("/my/path");
        when(request.getMethod()).thenReturn("POST");
        when(request.getReader()).thenReturn(new BufferedReader(new StringReader(expectedPayload)));
        when(user.getSession()).thenReturn(session);

        RedirectUtils.saveHttpPostPayload(request);
        when(session.getAttribute(HTTP_POST_PAYLOAD)).thenReturn("attributes[vrn]=W90MOG&categoryId=1991");

        // when
        ModelAndView view = controller.showLoginPage(user, "", false, request);

        // then
        assertThat(((LoginModel) view.getModel().get("model")).getCore().getGaEventElements().get(0).getLabel(), equalTo("VrmWidget"));
    }

    @Test
    public void whenNotRedirectedFromVRMWidgetTheGAEventLabelShouldBeNull() throws IOException {
        //        Given
        String expectedPayload = "categoryId=1991";
        when(request.getRequestURL()).thenReturn(new StringBuffer("http://www.test.com/my/path"));
        when(request.getQueryString()).thenReturn("a=1&b=2&c=3");
        when(request.getRequestURI()).thenReturn("/my/path");
        when(request.getMethod()).thenReturn("POST");
        when(request.getReader()).thenReturn(new BufferedReader(new StringReader(expectedPayload)));
        when(user.getSession()).thenReturn(session);

        RedirectUtils.saveHttpPostPayload(request);
        when(session.getAttribute(HTTP_POST_PAYLOAD)).thenReturn("categoryId=1991");

        // when
        ModelAndView view = controller.showLoginPage(user, "", false, request);

        // then
        assertThat(((LoginModel) view.getModel().get("model")).getCore().getGaEventElements().get(0).getLabel(), equalTo(null));
    }

    private void assertMetricValue(int expectedValue, Action action, Status status) {
        Counter counter = meterRegistry.counter(NAME, ACTION, action.toString(), STATUS, status.toString());

        assertEquals(expectedValue, counter.count(), 0.1D);
    }
}
