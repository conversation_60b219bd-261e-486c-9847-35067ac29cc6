package com.gumtree.web.seller.page.payment.braintree;

import com.gumtree.api.User;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.seller.domain.payment.entity.PaymentMethod;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.payment.api.PaymentApiClient;
import com.gumtree.web.seller.page.payment.event.PaymentEventsListener;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;
import com.gumtree.web.seller.service.payment.OrderAlreadyPaidException;
import com.gumtree.web.seller.service.payment.PaymentDeclinedException;
import org.junit.Before;
import org.junit.Test;
import org.skyscreamer.jsonassert.JSONAssert;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.IsEqual.equalTo;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class BraintreePaymentServiceTest {

    private BraintreePaymentService braintreePaymentService;

    private BushfireApi bushfireApi;
    private PaymentEventsListener paymentEvents;
    private PaymentApiClient paymentApiClient;

    private Long orderId = 111L;

    @Before
    public void setup() throws Exception {
        paymentApiClient = mock(PaymentApiClient.class);
        bushfireApi = mock(BushfireApi.class);
        paymentEvents = mock(PaymentEventsListener.class);
        braintreePaymentService = new BraintreePaymentService(paymentApiClient, bushfireApi, paymentEvents);
    }

    @Test
    public void getPaymentKeys() throws Exception {
        PaymentKeys paymentKeys = new PaymentKeys("merchantId", "token", "sandbox");
        when(paymentApiClient.intialisePayment(orderId)).thenReturn(paymentKeys);
        assertThat(braintreePaymentService.getPaymentKeys(orderId), equalTo(paymentKeys));
    }

    @Test
    public void paymentTransactionShouldReturnInvalidPaymentMethodWhenPaymentMethodNounceIsInvalid() throws Exception {

        String result = braintreePaymentService.paymentTransaction(null, null, null, null, null, null, "platformDevice");
        assertThat(result, equalTo("{\"errorMsg\":\"Please check your payment information and try again or try alternative payment methods\"}"));

        JSONAssert.assertEquals("{\"errorMsg\":\"Please check your payment information and try again or try alternative payment methods\"}", result, true);

        verify(paymentEvents).invalidPaymentMethodProblem(null, null);
    }


    @Test
    public void paymentTransactionShouldReturnsPaymentErrorMessageWhenRuntimeExceptionIsEncountered() throws Exception {

        Long orderId = 1L;
        ApiOrder order = new ApiOrder();
        order.setId(orderId);
        Checkout checkout = mock(Checkout.class);
        when(checkout.getOrder()).thenReturn(order);

        RuntimeException unexpectedException = new RuntimeException();
        doThrow(unexpectedException).when(paymentApiClient).executePayment(checkout, null, null, null, "paymentNonce", null, "platformDevice");

        String result = braintreePaymentService.paymentTransaction(checkout, "paymentNonce", null, null, null, null, "platformDevice");

        JSONAssert.assertEquals("{\"errorMsg\":\"Please check your payment information and try again or try alternative payment methods\"}", result, true);

        verify(paymentEvents).unexpectedPaymentProblemForOrder(orderId, unexpectedException, checkout);
    }

    @Test
    public void paymentTransactionShouldReturnsThankYouUrlWhenOrderAlreadyPaid() throws Exception {

        Long orderId = 1L;
        ApiOrder order = new ApiOrder();
        order.setId(orderId);
        Checkout checkout = mock(Checkout.class);
        when(checkout.getOrder()).thenReturn(order);

        doThrow(new OrderAlreadyPaidException(orderId)).when(paymentApiClient).executePayment(any(), any(), any(), any(), any(), any(), any());

        String result = braintreePaymentService.paymentTransaction(checkout, "paymentNonce", null, null, null, null, "platformDevice");

        JSONAssert.assertEquals("{\"url\":\"/thankyou/null\"}", result, true);

        verify(paymentEvents).alreadyPaidOrder(orderId);
    }


    @Test
    public void paymentTransactionShouldReturnsPaymentErrorMessageWhenTransactionFails() throws Exception {

        Long orderId = 1L;
        ApiOrder order = new ApiOrder();
        order.setId(orderId);
        Checkout checkout = mock(Checkout.class);
        when(checkout.getOrder()).thenReturn(order);

        PaymentDeclinedException paymentDeclinedException = new PaymentDeclinedException(orderId);
        doThrow(paymentDeclinedException).when(paymentApiClient).executePayment(any(), any(), any(), any(), any(), any(), any());

        String result = braintreePaymentService.paymentTransaction(checkout, "paymentNonce", null, null, null, null, "platformDevice");

        assertThat(result, equalTo("{\"errorMsg\":\"Please check your payment information and try again or try alternative payment methods\"}"));

        JSONAssert.assertEquals("{\"errorMsg\":\"Please check your payment information and try again or try alternative payment methods\"}", result, true);

        verify(paymentEvents).failedPaymentWithMessage(paymentDeclinedException.getMessage(), checkout);
    }


    @Test
    public void paymentTransactionUsingPaypalShouldReturnsThankYouUrlWhenTransactionIsSuccessful() throws Exception {
        testSuccessfulPaymentTransaction(PaymentMethod.BT_PAYPAL);

    }

    @Test
    public void paymentTransactionUsingCreditCardShouldReturnsThankYouUrlWhenTransactionIsSuccessful() throws Exception {
        testSuccessfulPaymentTransaction(PaymentMethod.BT_CARD);
    }

    private void testSuccessfulPaymentTransaction(PaymentMethod paymentMethod) {
        Long orderId = 1222L;
        ApiOrder order = new ApiOrder();
        order.setId(orderId);
        order.setTotalIncVat(11000L);
        order.setTotalIncVat(111L);
        Checkout checkout = mock(Checkout.class);
        when(checkout.getOrder()).thenReturn(order);

        String paymentMethodNonce = "paymentMethodNonce";
        User user = null;
        String deviceData = null;
        Long accountId = null;
        BillingAddress billingAddress = null;
        
        String result = braintreePaymentService.paymentTransaction(checkout, paymentMethodNonce, user, deviceData, accountId, billingAddress, "platformDevice");

        JSONAssert.assertEquals("{\"url\":\"/thankyou/null\"}", result, true);

        verify(paymentEvents).successfulPaymentForTransaction(order.getId());
    }

}