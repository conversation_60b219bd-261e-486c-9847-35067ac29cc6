package com.gumtree.web.seller.page.payment.controller;

import com.google.common.base.Optional;
import com.google.common.collect.Lists;
import com.gumtree.api.BushfireApiKey;
import com.gumtree.api.User;
import com.gumtree.api.category.domain.Category;
import com.gumtree.api.client.spec.OrderApi;
import com.gumtree.api.domain.order.ApiOrder;
import com.gumtree.api.domain.order.ApiOrderItem;
import com.gumtree.api.domain.payment.ApiPaymentDetail;
import com.gumtree.domain.location.Location;
import com.gumtree.seller.domain.order.status.OrderStatus;
import com.gumtree.seller.domain.payment.entity.PaymentMethod;
import com.gumtree.web.abtest.Experiments;
import com.gumtree.web.common.domain.order.Order;
import com.gumtree.web.common.domain.order.OrderItem;
import com.gumtree.web.common.domain.order.converter.ApiOrderToOrderEntityConverter;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.BaseSellerControllerTest;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.page.manageads.model.Checkout;
import com.gumtree.web.seller.page.manageads.model.CheckoutAdvert;
import com.gumtree.web.seller.page.payment.braintree.PaymentService;
import com.gumtree.web.seller.page.payment.model.BillingAddress;
import com.gumtree.web.seller.page.payment.model.PaymentKeys;
import com.gumtree.web.seller.page.postad.model.PaymentCheckoutModel;
import com.gumtree.web.seller.page.postad.model.meta.MetaPathInfo;
import com.gumtree.web.seller.page.postad.model.meta.PageActionType;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.netflix.config.ConfigurationManager;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.joda.time.DateTimeUtils;
import org.json.JSONException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyLong;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

@SuppressWarnings("OptionalGetWithoutIsPresent")
@RunWith(MockitoJUnitRunner.class)
public class PaymentCheckoutControllerTest extends BaseSellerControllerTest {
    private static final long USER_ID = 1L;
    private static final long ORDER_ID = 122L;
    private PaymentCheckoutController controller;
    private ApiOrderToOrderEntityConverter converter;
    private UserSession authenticatedUserSession;
    private Checkout checkout;
    private PaymentService paymentService;
    private CheckoutContainer checkoutContainer;

    @Before
    public void setup() {
        converter = mock(ApiOrderToOrderEntityConverter.class);
        checkout = mock(Checkout.class);

        BushfireApiKey apiKey = new BushfireApiKey();

        User user = new User();
        user.setId(USER_ID);
        authenticatedUserSession = mock(UserSession.class);
        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(1L);
        when(authenticatedUserSession.getApiKey()).thenReturn(apiKey);
        when(authenticatedUserSession.getUser()).thenReturn(user);
        when(authenticatedUserSession.getSelectedAccountId()).thenReturn(1234L);

        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setId(ORDER_ID);
        apiOrder.setItems(Lists.newArrayList());
        when(checkout.getOrder()).thenReturn(apiOrder);
        checkoutContainer = mock(CheckoutContainer.class);
        when(checkoutContainer.createCheckout(eq(apiOrder))).thenReturn(checkout);
        when(checkoutContainer.getCheckout(anyString())).thenReturn(checkout);

        OrderApi orderApi = mock(OrderApi.class);
        when(orderApi.getOrder(anyLong())).thenReturn(apiOrder);

        Order order = mock(Order.class);
        when(converter.convert(apiOrder)).thenReturn(order);
        DateTimeUtils.setCurrentMillisFixed(1000000);

        paymentService = mock(PaymentService.class);
        when(pageContext.getDeviceType()).thenReturn("desktop-computer");
        controller =
                new PaymentCheckoutController(cookieResolver, categoryModel, apiCallExecutor, messageResolver,
                        urlScheme, bushfireApi, converter, checkoutContainer, authenticatedUserSession, pageContext,
                        categoryService, locationService, "http://baseuri", zenoService, paymentService,
                        userSessionService, new CustomMetricRegistry(new SimpleMeterRegistry()));

        autowireAbExperimentsService(controller);

        Properties properties = new Properties();
        properties.setProperty("gumtree.host", "http://localhost:8080");
        ConfigurationManager.loadProperties(properties);
    }

    /*
     *  Checkout Page Tests
     */
    @Test
    public void checkoutPagePopulatesReturnUrlForResponsiveWithNoCredits() {
        ApiPaymentDetail details = new ApiPaymentDetail();
        details.setPaymentMethod(PaymentMethod.INVOICE);
        ApiOrderItem item = new ApiOrderItem();
        item.setPaymentDetail(details);
        item.setPriceIncVat(1900L);
        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setId(1L);
        apiOrder.setItems(Lists.newArrayList(item));
        when(checkout.getOrder()).thenReturn(apiOrder);
        when(checkout.getAdvert()).thenReturn(null);

        when(paymentService.getPaymentKeys(apiOrder.getId(),authenticatedUserSession.getUser().getId())).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));
        when(paymentService.getPaymentKeys(apiOrder.getId())).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));

        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        PaymentCheckoutModel model = getModel(modelAndView);
        assertTrue(model.isPaymentFormEnabled());
        verifyZeroInteractions(pageContext);
    }

    @Test
    public void checkoutPagePopulatesReturnUrlForResponsive() {
        when(checkout.getAdvert()).thenReturn(null);
        when(paymentService.getPaymentKeys(ORDER_ID,USER_ID)).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));
        when(paymentService.getPaymentKeys(ORDER_ID)).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));
        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        PaymentCheckoutModel model = getModel(modelAndView);
        assertFalse(model.isPaymentFormEnabled());
        verifyZeroInteractions(pageContext);
    }

    @Test
    public void checkoutPageModelContainsBraintreeClientTokenIfIsInBraintreeTestGroup() {
        when(paymentService.getPaymentKeys(ORDER_ID,USER_ID)).thenReturn(new PaymentKeys("merchantId", "braintreeClientToken", "sandbox"));
        when(paymentService.getPaymentKeys(ORDER_ID)).thenReturn(new PaymentKeys("merchantId", "braintreeClientToken", "sandbox"));
        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        PaymentCheckoutModel model = getModel(modelAndView);
        assertThat(model.getClientBraintreeToken().toString(), equalTo("braintreeClientToken"));
        verifyZeroInteractions(pageContext);
    }

    @Test
    public void checkoutPageModelContainsOrder() {
        Order order = mock(Order.class);
        OrderItem orderItem = mock(OrderItem.class);
        when(order.getItems()).thenReturn(Collections.singletonList(orderItem));
        when(converter.convert(any(ApiOrder.class))).thenReturn(order);
        CheckoutAdvert checkoutAdvert = CheckoutAdvert.Builder.builder()
                .id(1L)
                .locationId(23L)
                .categoryId(42L)
                .build();
        when(checkout.getAdvert()).thenReturn(checkoutAdvert);

        Location location = mock(Location.class);
        when(locationService.getById(23)).thenReturn(location);
        Category category = mock(Category.class);
        when(categoryService.getById(42L)).thenReturn(Optional.of(category));
        Map<Integer, Category> categoryHierarchy = mock(Map.class);
        when(categoryService.getLevelHierarchy(category)).thenReturn(categoryHierarchy);
        when(paymentService.getPaymentKeys(ORDER_ID,USER_ID)).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));
        when(paymentService.getPaymentKeys(ORDER_ID)).thenReturn(new PaymentKeys("merchantId", "token", "sandbox"));

        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        PaymentCheckoutModel model = getModel(modelAndView);

        assertThat(model.getOrder(), equalTo(order));
        assertThat(model.getOrder().getItems().get(0), equalTo(orderItem));
        verify(pageContext).setCategory(category);
        verify(pageContext).setCategoryLevelHierarchy(categoryHierarchy);
        verify(pageContext).setLocation(location);
    }

    @Test
    public void shouldRedirectToThankYOuUrlWhenNoAdvertFoundOnTheCheckout() {
        //given
        preparePAIDCheckout();
        when(checkout.getAdvert()).thenReturn(null);

        String expexctedUrl = "/thankyou/123four?action=post&payment=none&type=none&multiple=no";
        //when
        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        RedirectView redirectView = (RedirectView) modelAndView.getView();

        //then
        assertEquals(expexctedUrl, redirectView.getUrl());

    }

    @Test
    public void shouldRedirectToThankYouUrlWhenOnboardingExperimentDisabled() {
        //given
        preparePAIDCheckout();
        CheckoutAdvert checkoutAdvert = CheckoutAdvert.Builder.builder().id(1L).build();
        when(checkout.getAdvert()).thenReturn(checkoutAdvert);

        //and
        Map<String, String> emptyExperimentMap = new HashMap<>();
        Experiments experiments = new Experiments(emptyExperimentMap);
        when(experimentsProvider.get()).thenReturn(experiments);

        String expexctedUrl = "/thankyou/123four?action=post&payment=none&type=none&multiple=no&advertId=1";

        //when
        ModelAndView modelAndView = controller.checkoutPage("123four", request);
        RedirectView redirectView = (RedirectView) modelAndView.getView();

        //then
        assertEquals(expexctedUrl, redirectView.getUrl());

    }

    /*
     * Payment  Transaction Tests
     */

    @Test
    public void shouldReturnPaymentResponseWithValidCheckoutKey()
            throws JSONException {
        BillingAddress billingAddress = BillingAddress.builder()
                .withTownCity("Town")
                .withAddress("Address")
                .withPostcode("Postcode")
                .withCountry("Country")
                .withFirstName("FirstName")
                .withLastName("LastName")
                .build();

        PaymentCheckoutController.DEVICETYPE deviceType = PaymentCheckoutController.DEVICETYPE.fromLabel(pageContext.getDeviceType());
        String platform = "responsive";
        String platformDevice = String.format("%s_%s", platform, deviceType.toString().toLowerCase());

        when(paymentService.paymentTransaction(checkout, "paymentMethod",
                authenticatedUserSession.getUser(),
                "mid",
                authenticatedUserSession.getSelectedAccountId(),
                billingAddress, platformDevice)).thenReturn("{\"url\":\"/thankyou/checkoutKey\"}");

        String result = controller.paymentTransaction("checkoutKey",
                "paymentMethod", "mid",
                billingAddress.getFirstName().get(),
                billingAddress.getLastName().get(),
                billingAddress.getCountry().get(),
                billingAddress.getAddress().get(),
                billingAddress.getTownCity().get(),
                billingAddress.getPostcode().get());

        JSONAssert.assertEquals("{\"url\":\"/thankyou/checkoutKey\"}", result, true);
    }

    @Test
    public void paymentTxnRedirectToDefaultUrlWhenOnBoardingExperimentDisabled() {
        //given
        CheckoutAdvert checkoutAdvert = createCheckOutAdvert();
        when(checkout.getAdvert()).thenReturn(checkoutAdvert);

        //and
        Map<String, String> emptyExperimentMap = new HashMap<>();
        Experiments experiments = new Experiments(emptyExperimentMap);
        when(experimentsProvider.get()).thenReturn(experiments);

        //and
        BillingAddress billingAddress = BillingAddress.builder()
                .withTownCity("Town")
                .withAddress("Address")
                .withPostcode("Postcode")
                .withCountry("Country")
                .withFirstName("FirstName")
                .withLastName("LastName")
                .build();

        PaymentCheckoutController.DEVICETYPE deviceType = PaymentCheckoutController.DEVICETYPE.fromLabel(pageContext.getDeviceType());
        String platform = "responsive";
        String platformDevice = String.format("%s_%s", platform, deviceType.toString().toLowerCase());

        when(paymentService.paymentTransaction(checkout, "paymentMethod",
                authenticatedUserSession.getUser(),
                "mid",
                authenticatedUserSession.getSelectedAccountId(),
                billingAddress, platformDevice)).thenReturn("{\"url\":\"/thankyou/checkoutKey\"}");

        //when
        String result = controller.paymentTransaction("checkoutKey",
                "paymentMethod", "mid",
                billingAddress.getFirstName().get(),
                billingAddress.getLastName().get(),
                billingAddress.getCountry().get(),
                billingAddress.getAddress().get(),
                billingAddress.getTownCity().get(),
                billingAddress.getPostcode().get());

        //then
        JSONAssert.assertEquals("{\"url\":\"/thankyou/checkoutKey\"}", result, true);
    }

    private void preparePAIDCheckout() {
        MetaPathInfo metaPathInfo = new MetaPathInfo();
        metaPathInfo.setIsMultipleAds(false);
        metaPathInfo.setPageActionType(PageActionType.POST);
        metaPathInfo.setFeatureTypes(new ArrayList<>());
        metaPathInfo.setPagePaymentTypes(new ArrayList<>());

        when(checkoutContainer.getCheckout(anyString())).thenReturn(checkout);

        ApiOrder apiOrder = new ApiOrder();
        apiOrder.setId(ORDER_ID);
        apiOrder.setItems(Lists.newArrayList());
        apiOrder.setStatus(OrderStatus.PAID);
        when(checkout.getOrder()).thenReturn(apiOrder);
        when(checkout.getMetaPathInfo()).thenReturn(metaPathInfo);
        when(checkout.getKey()).thenReturn("123four");
    }

    private PaymentCheckoutModel getModel(ModelAndView modelAndView) {
        assertNotNull(modelAndView.getModelMap().get("model"));
        return (PaymentCheckoutModel) modelAndView.getModelMap().get("model");
    }

    private CheckoutAdvert createCheckOutAdvert() {
        return CheckoutAdvert.Builder.builder()
                .id(1L)
                .title("testTitle")
                .categoryId(2L)
                .locationId(1234L).build();
    }
}
