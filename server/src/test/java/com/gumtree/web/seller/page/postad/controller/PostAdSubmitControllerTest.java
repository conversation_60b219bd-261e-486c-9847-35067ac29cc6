package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.web.seller.page.postad.controller.steps.CategorySelectPostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.ImagesPostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.LastPostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.LegalPostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.LocationAndSellerTypePostAdStep;
import com.gumtree.web.seller.page.postad.controller.steps.RateLimiterPostAdStep;
import org.junit.Test;

import static org.fest.assertions.api.Assertions.assertThat;

public class PostAdSubmitControllerTest {

    @Test
    public void stepsAreOrderedCorrectly() {
        assertThat(CategorySelectPostAdStep.ORDER).isEqualTo(1);
        assertThat(LegalPostAdStep.ORDER).isEqualTo(2);
        assertThat(LocationAndSellerTypePostAdStep.ORDER).isEqualTo(3);
        assertThat(RateLimiterPostAdStep.ORDER).isEqualTo(4);
        assertThat(ImagesPostAdStep.ORDER).isEqualTo(5);
        assertThat(LastPostAdStep.ORDER).isEqualTo(6);
    }
}