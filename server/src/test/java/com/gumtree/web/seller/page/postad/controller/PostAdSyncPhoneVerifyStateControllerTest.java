package com.gumtree.web.seller.page.postad.controller;

import com.gumtree.api.Ad;
import com.gumtree.api.AdStatus;
import com.gumtree.api.client.BushfireApi;
import com.gumtree.api.client.spec.AdvertApi;
import com.gumtree.security.phone.number.authenticator.model.AuthenticationStatus;
import com.gumtree.web.cookie.CookieResolver;
import com.gumtree.web.security.UserSession;
import com.gumtree.web.seller.page.manageads.metric.CustomMetricRegistry;
import com.gumtree.web.seller.service.CheckoutContainer;
import com.gumtree.web.seller.service.CheckoutMetaInjector;
import com.gumtree.web.seller.service.PostAdWorkspace;
import com.gumtree.web.seller.service.phoneverify.PhoneVerifyService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.servlet.ModelAndView;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PostAdSyncPhoneVerifyStateControllerTest {

    private MockMvc mockMvc;

    @Mock
    private BushfireApi bushfireApi;

    @Mock
    private PhoneVerifyService phoneVerifyService;

    @Mock
    private CookieResolver cookieResolver;

    @Mock
    private CheckoutContainer checkoutContainer;

    @Mock
    private CheckoutMetaInjector checkoutMetaInjector;

    @Mock
    private PostAdWorkspace postAdWorkspace;

    @Mock
    private AdvertApi advertApi;

    @Mock
    private UserSession userSession;

    @Mock
    private CustomMetricRegistry metric;

    private AuthenticationStatus status=AuthenticationStatus.VERIFIED;

    @InjectMocks
    private PostAdSyncPhoneVerifyStateController postAdSyncPhoneVerifyStateController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(postAdSyncPhoneVerifyStateController).build();
    }

    @Test
    public void syncPhoneVerifyState_StatusNotMatch_ReturnsStatusNotMatch() throws Exception {
        Long userId = 1L;
        String emailAddress = "<EMAIL>";
        Long adId = 100L;
        String editorId = "editor1";

        Ad ad = new Ad();
        ad.setId(adId);
        ad.setTitle("tmp title");
        ad.setUserId(userId);
        ad.setStatus(AdStatus.LIVE);

        when(bushfireApi.advertApi()).thenReturn(advertApi);
        when(advertApi.getAdvert(adId)).thenReturn(ad);
        ModelAndView modelAndView = postAdSyncPhoneVerifyStateController.handSync(userId,adId,editorId,null);
        Assert.assertTrue(modelAndView.getModelMap().get("bizCode").equals("STATUS_NOT_MATCH"));
    }

    @Test
    public void syncPhoneVerifyState_UserNotVerified_ReturnsUserUnverified() throws Exception {
        Long userId = 1L;
        String emailAddress = "<EMAIL>";
        Long adId = 100L;
        String editorId = "editor1";

        Ad ad = new Ad();
        ad.setUserId(userId);
        ad.setId(adId);
        ad.setTitle("tmp title");
        ad.setStatus(AdStatus.AWAITING_PHONE_VERIFIED);

        when(bushfireApi.advertApi()).thenReturn(advertApi);
        when(advertApi.getAdvert(adId)).thenReturn(ad);

        when(phoneVerifyService.getAuthenticationStatus(userId))
                .thenReturn(AuthenticationStatus.UNVERIFIED);
        ModelAndView modelAndView = postAdSyncPhoneVerifyStateController.handSync(userId,adId,editorId,null);
        Assert.assertTrue(modelAndView.getModelMap().get("bizCode").equals("USER_UNVERIFIED"));
    }
}
