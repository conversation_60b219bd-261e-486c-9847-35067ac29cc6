package com.gumtree.config.api;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;

import static java.util.stream.IntStream.range;


public final class SellerStubWireMockApi {
    protected static final Logger LOG = LoggerFactory.getLogger(SellerStubWireMockApi.class);

    public static final int WIREMOCK_API_PORT = getAvailablePort(8300);

    private static final WireMockServer SERVER = new WireMockServer(WireMockConfiguration.wireMockConfig().port(WIREMOCK_API_PORT));

    static {
        SERVER.start();
        SERVER.resetMappings();
    }

    private SellerStubWireMockApi() {
    }

    //TODO the refactor will replace this with dynamicHttpsPort on the wiremock.
    public static int getAvailablePort(int startPort) {
        int availablePort = range(startPort, 65000)
                .filter(SellerStubWireMockApi::portAvailable)
                .findFirst()
                .orElseThrow(() -> {
                    LOG.error("Could not find Free port for tests.");
                    return new RuntimeException("Could not find Free port for tests.");
                });

        return availablePort;
    }

    public static boolean portAvailable(int i) {
        LOG.info("Checking if a port {} is available", i);
        ServerSocket socket = null;
        try {
            socket = new ServerSocket(i);
            LOG.info("Found available port: {}", i);
            return true;
        } catch (IOException e) {
            LOG.info("Port {} is NOT available", i);
            return false;
        } finally {
            try {
                if (socket != null) socket.close();
            } catch (IOException e) {
                LOG.error("Failed to close socket", e);
                /// left empty ... no error catching.
            }
        }
    }

    public static WireMockServer getServer() {
        return SERVER;
    }
}
