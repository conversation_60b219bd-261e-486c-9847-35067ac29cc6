package com.gumtree.web.common.domain.messagecentre;

import com.gumtree.web.common.domain.ad.ReplyType;
import org.apache.http.util.Asserts;

import java.util.Objects;

public final class Advert {

    private final Long id;
    private final String title;
    private final String url;
    private final String img;
    private final String status;
    private final String date;
    private final String price;
    private final String priceFrequency;
    private final Long categoryId;
    private final boolean proAccount;
    private final Long accountId;
    private final Long l1CategoryId;
    private final Long l2CategoryId;
    private final ReplyType replyType;
    private final boolean markedAsSold;
    private final String vrm;
    private final String sellerType;
    private final Boolean vehicleVhcChecked;

    private Advert(Builder builder) {
        Asserts.notNull(builder.categoryId, "Category Id is required");

        this.id = builder.id;
        this.title = builder.title;
        this.url = builder.url;
        this.img = builder.img;
        this.date = builder.date;
        this.price = builder.price;
        this.priceFrequency = builder.priceFrequency;
        this.status = builder.status;
        this.categoryId = builder.categoryId;
        this.proAccount = builder.proAccount;
        this.accountId = builder.accountId;
        this.l1CategoryId = builder.l1CategoryId;
        this.l2CategoryId = builder.l2CategoryId;
        this.replyType = builder.replyType;
        this.markedAsSold = builder.markedAsSold;
        this.sellerType = builder.sellerType;
        this.vrm = builder.vrm;
        this.vehicleVhcChecked = builder.vehicleVhcChecked;
    }

    public Long getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getUrl() {
        return url;
    }

    public String getImg() {
        return img;
    }

    public String getDate() {
        return date;
    }

    public String getPrice() {
        return price;
    }

    public String getPriceFrequency() {
        return priceFrequency;
    }

    public String getStatus() {
        return status;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public Long getL1CategoryId() {
        return l1CategoryId;
    }

    public Long getL2CategoryId() {
        return l2CategoryId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public boolean isProAccount() {
        return proAccount;
    }

    public ReplyType getReplyType() {
        return replyType;
    }

    public boolean isMarkedAsSold() {
        return markedAsSold;
    }

    public String getVrm() {
        return vrm;
    }

    public String getSellerType() {
        return sellerType;
    }

    public Boolean getVehicleVhcChecked() {
        return vehicleVhcChecked;
    }

    public static class Builder {

        private Long id;
        private String title;
        private String url;
        private String img;
        private String status;
        private String date;
        private String price;
        private String priceFrequency;
        private Long categoryId;
        private boolean proAccount;
        private Long accountId;
        private Long l1CategoryId;
        private Long l2CategoryId;
        private ReplyType replyType = ReplyType.GUMTREE;
        private boolean markedAsSold;
        private String sellerType;
        private String vrm;
        private Boolean vehicleVhcChecked;

        public Builder setId(Long id) {
            this.id = id;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setImg(String img) {
            this.img = img;
            return this;
        }

        public Builder setDate(String date) {
            this.date = date;
            return this;
        }

        public Builder setPrice(String price) {
            this.price = price;
            return this;
        }

        public Builder setPriceFrequency(String priceFrequency) {
            this.priceFrequency = priceFrequency;
            return this;
        }

        public Builder setStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder setCategoryId(Long categoryId) {
            this.categoryId = categoryId;
            return this;
        }

        public Builder setL1CategoryId(Long categoryId) {
            this.l1CategoryId = categoryId;
            return this;
        }

        public Builder setL2CategoryId(Long categoryId) {
            this.l2CategoryId = categoryId;
            return this;
        }

        public Builder setProAccount(Boolean proAccount) {
            this.proAccount = proAccount;
            return this;
        }

        public Builder setAccountId(Long accountId) {
            this.accountId = accountId;
            return this;
        }

        public Builder setReplyType(ReplyType replyType) {
            this.replyType = replyType;
            return this;
        }

        public Builder setMarkedAsSold(boolean markedAsSold) {
            this.markedAsSold = markedAsSold;
            return this;
        }

        public Builder setVrm(String vrm) {
            this.vrm = vrm;
            return this;
        }

        public Builder setSellerType(String sellerType) {
            this.sellerType = sellerType;
            return this;
        }

        public Builder setVehicleVhcChecked(Boolean vehicleVhcChecked) {
            this.vehicleVhcChecked = vehicleVhcChecked;
            return this;
        }

        public Advert build() {
            return new Advert(this);
        }
    }

    @Override
    public String toString() {
        return "Advert{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", url='" + url + '\'' +
                ", img='" + img + '\'' +
                ", status='" + status + '\'' +
                ", date='" + date + '\'' +
                ", price='" + price + '\'' +
                ", priceFrequency='" + priceFrequency + '\'' +
                ", categoryId=" + categoryId +
                ", proAccount=" + proAccount +
                ", accountId=" + accountId +
                ", l1CategoryId=" + l1CategoryId +
                ", l2CategoryId=" + l2CategoryId +
                ", replyType=" + replyType +
                ", markedAsSold=" + markedAsSold +
                ", vrm='" + vrm + '\'' +
                ", sellerType='" + sellerType + '\'' +
                ", vehicleVhcChecked='" + vehicleVhcChecked + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Advert advert = (Advert) o;
        return proAccount == advert.proAccount &&
                Objects.equals(id, advert.id) &&
                Objects.equals(title, advert.title) &&
                Objects.equals(url, advert.url) &&
                Objects.equals(img, advert.img) &&
                Objects.equals(status, advert.status) &&
                Objects.equals(date, advert.date) &&
                Objects.equals(price, advert.price) &&
                Objects.equals(priceFrequency, advert.priceFrequency) &&
                Objects.equals(categoryId, advert.categoryId) &&
                Objects.equals(accountId, advert.accountId) &&
                Objects.equals(l1CategoryId, advert.l1CategoryId) &&
                Objects.equals(l2CategoryId, advert.l2CategoryId) &&
                Objects.equals(markedAsSold, advert.markedAsSold) &&
                Objects.equals(sellerType, advert.sellerType) &&
                Objects.equals(vrm, advert.vrm) &&
                Objects.equals(vehicleVhcChecked, advert.vehicleVhcChecked) &&
                replyType == advert.replyType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, title, url, img, status, date, price, priceFrequency,
                categoryId, proAccount, accountId, l1CategoryId, l2CategoryId, replyType, markedAsSold,
                vrm, sellerType, vehicleVhcChecked);
    }

}
