package com.gumtree.web.common.domain.messagecentre;


import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

public final class Conversations {
    private final int numUnreadConversations;
    private final int numConversations;
    private final PollingFrequencies pollingFrequencies;
    private final List<ConversationGroup> conversationGroups;

    public static Builder builder() {
        return new Builder();
    }

    private Conversations(Builder builder) {
        this.numUnreadConversations = builder.numUnreadConversations;
        this.numConversations = builder.numConversations;
        this.conversationGroups = builder.conversationGroups;
        this.pollingFrequencies = builder.pollingFrequencies;
    }

    public static class Builder {

        private int numUnreadConversations;
        private int numConversations;
        private PollingFrequencies pollingFrequencies;
        private List<ConversationGroup> conversationGroups;

        public Builder setNumUnreadConversations(int numUnreadConversations) {
            this.numUnreadConversations = numUnreadConversations;
            return this;
        }

        public Builder setNumConversations(int numConversations) {
            this.numConversations = numConversations;
            return this;
        }

        public Builder setConversationGroups(List<ConversationGroup> conversationGroups) {
            this.conversationGroups = conversationGroups;
            return this;
        }

        public Builder setPollingFrequencies(PollingFrequencies pollingFrequencies) {
            this.pollingFrequencies = pollingFrequencies;
            return this;
        }

        public Conversations build() {
            return new Conversations(this);
        }
    }

    public int getNumUnreadConversations() {
        return numUnreadConversations;
    }

    public int getNumConversations() {
        return numConversations;
    }

    public List<ConversationGroup> getConversationGroups() {
        return null != conversationGroups ? conversationGroups : Collections.emptyList();
    }

    public PollingFrequencies getPollingFrequencies() {
        return pollingFrequencies;
    }

    public String getLastConversationId() {
        return CollectionUtils.isNotEmpty(conversationGroups) ? conversationGroups.get(0).getLastConversationId() : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Conversations)) return false;

        Conversations that = (Conversations) o;

        if (numUnreadConversations != that.numUnreadConversations) return false;
        if (numConversations != that.numConversations) return false;
        if (pollingFrequencies != null
                ? !pollingFrequencies.equals(that.pollingFrequencies) : that.pollingFrequencies != null) {
            return false;
        }
        return conversationGroups != null
                ? conversationGroups.equals(that.conversationGroups) : that.conversationGroups == null;
    }

    @Override
    public int hashCode() {
        int result = numUnreadConversations;
        result = 31 * result + numConversations;
        result = 31 * result + (pollingFrequencies != null ? pollingFrequencies.hashCode() : 0);
        result = 31 * result + (conversationGroups != null ? conversationGroups.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Conversations{" +
                "numUnreadConversations=" + numUnreadConversations +
                ", numConversations=" + numConversations +
                ", pollingFrequencies=" + pollingFrequencies +
                ", conversationGroups=" + conversationGroups +
                '}';
    }
}
