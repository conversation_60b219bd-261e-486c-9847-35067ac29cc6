package com.gumtree.web.common.error.json;

/**
 * Root model for JSON validation responses.
 */
public final class JsonValidationResponse {

    private JsonValidation validation;

    /**
     * Constructor.
     *
     * @param validation the validation model.
     */
    public JsonValidationResponse(JsonValidation validation) {
        this.validation = validation;
    }

    public JsonValidation getValidation() {
        return validation;
    }

    public void setValidation(JsonValidation validation) {
        this.validation = validation;
    }
}
